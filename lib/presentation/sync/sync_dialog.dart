import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../app/assets_manager.dart';
import '../../app/theme/colors.dart';
import '../../core/providers/sync_notifier.dart';
import '../../core/widgets/dashed_divider.dart';

class SyncDialog extends ConsumerWidget {
  const SyncDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
     // Ensure sync is initialized
    final syncNotifier = ref.watch(syncNotifierProvider.notifier);
    final syncState = ref.watch(syncNotifierProvider);
    final categories = syncNotifier.categories;
    final progress = syncNotifier.progress;
    final items = syncNotifier.items;

    // Calculate category counts
    final cookbookCount = items.where((item) => item['category'] == 'Cookbooks').length;
    final shoppingCount = items.where((item) => item['category'] == 'Shopping').length;

    // Filter items based on selected categories
    final filteredItems = items.where((item) {
      final category = item['category'] as String? ?? 'Cookbooks';
      return categories[category] ?? false;
    }).toList();

    // Group items by category for display
    final cookbookItems = filteredItems.where((item) => item['category'] == 'Cookbooks').toList();
    final shoppingItems = filteredItems.where((item) => item['category'] == 'Shopping').toList();

    // Build list items with subheadings, including only if category is checked
    final listItems = <dynamic>[
      if (categories['Cookbooks'] == true && cookbookItems.isNotEmpty) 'Cookbooks Items',
      ...cookbookItems,
      if (categories['Shopping'] == true && shoppingItems.isNotEmpty) 'Shopping Items',
      ...shoppingItems,
    ];

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white,
        ),
        padding: const EdgeInsets.all(24),
        width: MediaQuery.of(context).size.width * 0.80,
        height: MediaQuery.of(context).size.height * 0.75,
        constraints: BoxConstraints(
          minWidth: 300,
          maxWidth: MediaQuery.of(context).size.width * 0.90,
          minHeight: 400,
          maxHeight: MediaQuery.of(context).size.height * 0.90,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText(
                  text: 'Welcome Back, Chef!',
                  size: 24,
                  weight: FontWeight.w600,
                  color: AppColors.primaryLightTextColor,
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: AppColors.primaryLightTextColor),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            CustomText(
              text:
              "We’ve found your saved recipes, cookbooks, and shopping lists. Sync now to access everything across all your devices — seamlessly and securely.",
              weight: FontWeight.w400,
              size: 16,
              color: AppColors.primaryLightTextColor,
            ),
            const SizedBox(height: 16),
            const DashedDivider(thickness: 2),
            const SizedBox(height: 16),

            // Two-column layout
            Expanded(
              child: Row(
                children: [
                  // Left Column: Categories
                  Expanded(
                    flex: 2,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            spreadRadius: 2,
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: categories.entries.map((entry) {
                          return CheckboxListTile(
                            activeColor: AppColors.primaryColor,
                            value: entry.value,
                            onChanged: (val) {
                              syncNotifier.toggleCategory(entry.key, val!);
                            },
                            title: CustomText(
                              text: entry.key,
                              weight: FontWeight.w400,
                              size: 20,
                              color: AppColors.primaryLightTextColor,
                            ),
                            subtitle: CustomText(
                              text: _getSubtitle(entry.key, cookbookCount, shoppingCount),
                              weight: FontWeight.w400,
                              size: 14,
                              color: AppColors.primaryLightTextColor,
                            ),
                            secondary: _getIcon(entry.key),
                            controlAffinity: ListTileControlAffinity.trailing,
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 24),

                  // Right Column: Sync List
                  Expanded(
                    flex: 3,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            spreadRadius: 2,
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 8),
                          Expanded(
                            child: filteredItems.isEmpty
                                ? const Center(
                              child: Text(
                                'No items to sync. Select a category to view items.',
                                style: TextStyle(
                                  color: AppColors.primaryLightTextColor,
                                  fontSize: 16,
                                ),
                              ),
                            )
                                : ListView.builder(
                              itemCount: listItems.length,
                              itemBuilder: (context, index) {
                                final item = listItems[index];
                                if (item is String) {
                                  // Subheading
                                  return Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                                        child: SizedBox(
                                          width: double.infinity,
                                          child: CustomText(
                                            text: item,
                                            weight: FontWeight.w600,
                                            size: 16,
                                            color: AppColors.primaryLightTextColor,
                                          ),
                                        ),
                                      ),
                                      const Divider(),
                                    ],
                                  );
                                } else {
                                  // Item
                                  return Column(
                                    children: [
                                      CheckboxListTile(
                                        activeColor: AppColors.primaryColor,
                                        value: item['isSelected'] ?? false,
                                        onChanged: (val) {
                                          syncNotifier.toggleItem(item['name'], val!);
                                        },
                                        title: CustomText(
                                          text: item['name'],
                                          weight: FontWeight.w400,
                                          size: 16,
                                          color: AppColors.primaryLightTextColor,
                                        ),
                                        subtitle: CustomText(
                                          text: _getItemSubtitle(item),
                                          weight: FontWeight.w400,
                                          size: 14,
                                          color: AppColors.primaryLightTextColor,
                                        ),
                                        controlAffinity: ListTileControlAffinity.trailing,
                                      ),
                                      const Divider(),
                                    ],
                                  );
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),
            // Buttons & Benefits
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        text: 'Why Sync?',
                        weight: FontWeight.w500,
                        size: 20,
                        color: AppColors.primaryLightTextColor,
                      ),
                      const SizedBox(height: 16),
                      _buildSyncBenefits(context),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 4,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        text: '${filteredItems.length} items',
                        weight: FontWeight.w700,
                        size: 34,
                        color: AppColors.primaryLightTextColor,
                      ),
                      CustomText(
                        text: 'Syncing Recipes',
                        weight: FontWeight.w400,
                        size: 14,
                        color: AppColors.primaryLightTextColor,
                      ),
                      const SizedBox(height: 30),
                      CustomText(
                        text: progress > 0 ? 'Recipes are being synced...' : 'Ready to sync',
                        weight: FontWeight.w400,
                        size: 14,
                        color: AppColors.primaryLightTextColor,
                      ),
                      const SizedBox(height: 30),
                      CustomButton(
                        color: Colors.white,
                        textColor: AppColors.primaryColor,
                        text: 'Synchronization',
                        onPressed: () {
                          final selectedItemIds = syncNotifier.getSelectedItems();
                          print(
                              'Selected cookbookIds: ${selectedItemIds['cookbookIds']?.length ?? 0}, '
                                  'shoppingListIds: ${selectedItemIds['shoppingListIds']?.length ?? 0}');
                          if( selectedItemIds['cookbookIds']!.isEmpty &&
                              selectedItemIds['shoppingListIds']!.isEmpty) {
                          Utils().showFlushbar(context, message: 'Please select at least one item to sync.', isError: true);
                            return;
                          }
                          syncNotifier.syncSelectedItems(context, selectedItemIds);
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _getIcon(String key) {
    switch (key) {
      case 'Cookbooks':
        return SvgPicture.asset(AssetsManager.ic_cookbook);
      case 'Shopping':
        return SvgPicture.asset(AssetsManager.ic_shopping);
      default:
        return SvgPicture.asset(AssetsManager.ic_cookbook);
    }
  }

  String _getSubtitle(String key, int cookbookCount, int shoppingCount) {
    switch (key) {
      case 'Cookbooks':
        return '$cookbookCount item${cookbookCount == 1 ? '' : 's'} ready to import';
      case 'Shopping':
        return '$shoppingCount item${shoppingCount == 1 ? '' : 's'} ready to import';
      default:
        return '';
    }
  }

  String _getItemSubtitle(Map<String, dynamic> item) {
    final count = item['count'] ?? 0;
    final status = item['status'] ?? 'Syncing';
    return '$count item${count == 1 ? '' : 's'} • $status';
  }

  Widget _buildSyncBenefits(BuildContext context) {
    final List<String> features = [
      'Access Anywhere – Your recipes on any device',
      'Instant Updates – Changes reflect everywhere',
      'Cloud Backup – Never lose a recipe',
      'Private & Secure – Encrypted data',
    ];

    return Container(
      width: MediaQuery.of(context).size.width * 0.24,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: features.map((feature) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: '•',
                  weight: FontWeight.w400,
                  size: 14,
                  color: AppColors.primaryLightTextColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: CustomText(
                    text: feature,
                    weight: FontWeight.w400,
                    size: 14,
                    color: AppColors.primaryLightTextColor,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}