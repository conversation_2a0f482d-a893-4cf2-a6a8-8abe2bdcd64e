import 'package:flutter_svg/svg.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';

import '../../app/imports/packages_imports.dart';
import '../../core/data/models/get_ask_ai_response.dart';
import '../../core/helpers/thread_grouper.dart';
import '../../core/network/app_status.dart';
import '../../core/providers/gpt/ask_ai_notifier.dart';
import '../../core/utils/Utils.dart';
import '../../core/widgets/custom_searchbar.dart';
import '../../core/widgets/no_data_widget.dart';
import '../cookbook/widgets/custom_desc_text.dart';
import '../shimer/thread_list_shimmer.dart';

class Sidebar extends ConsumerStatefulWidget {
  final Function(Thread)? onThreadSelected;

  const Sidebar({super.key, this.onThreadSelected});

  @override
  ConsumerState<Sidebar> createState() => _SidebarState();
}

class _SidebarState extends ConsumerState<Sidebar> {
  int? selectedIndex;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final askAiState = ref.read(askAiNotifierProvider);
      print(
          'Scroll Debug: hasMore=${askAiState.hasMore}, status=${askAiState.status}, currentPage=${askAiState.currentPage}');
      if (askAiState.hasMore && askAiState.status != AppStatus.loadingMore) {
        print('Loading more threads - page ${askAiState.currentPage + 1}');
        ref.read(askAiNotifierProvider.notifier).getAskAiThreads(
              context: context,
              loadMore: true,
              search: _searchController.text.trim().isEmpty
                  ? null
                  : _searchController.text.trim(),
            );
      } else {
        print(
            'Not loading more: hasMore=${askAiState.hasMore}, status=${askAiState.status}');
      }
    }
  }

  // Handle search functionality
  void _onSearchChanged(String query) {
    if (mounted) {
      ref.read(askAiNotifierProvider.notifier).searchThreads(
            context: context,
            query: query,
          );
    }
  }

  // Clear search
  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _isSearching = false;
    });
    if (mounted) {
      ref.read(askAiNotifierProvider.notifier).clearSearch(context: context);
    }
  }

  // Handle delete thread
  Future<void> _handleDeleteThread(Thread thread) async {
    // Show confirmation dialog
    final bool? confirmed = await Utils().showCommonConfirmDialog(
      context: context,
      title: 'Delete Thread',
      subtitle: 'Are you sure you want to delete "${thread.title}" thread?',
      confirmText: 'Delete',
      cancelText: 'Cancel',
    );

    if (confirmed != true || !mounted) {
      return; // User cancelled deletion or context is no longer valid
    }

    // Call delete API
    await ref.read(askAiNotifierProvider.notifier).deleteThread(
      context: context,
      type: 'CUSTOM',
      threadIds: [thread.id],
    );

    // Refresh the threads list after successful deletion
    if (mounted) {
      await ref.read(askAiNotifierProvider.notifier).getAskAiThreads(
            context: context,
            loadMore: false,
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    final askAiState = ref.watch(askAiNotifierProvider);
    final isSmallScreen = MediaQuery.of(context).size.width <= 1100;

    final content = Container(
      color: Colors.white,
      child: Column(
        children: [
          SizedBox(height: 20.h),
          CustomSearchBar(
            controller: _searchController,
            hintText: "Search",
            width: DeviceUtils().isTabletOrIpad(context) ? 370 : 800.w,
            onChanged: (value) {
              setState(() {
                _isSearching = value.isNotEmpty;
              });
              _onSearchChanged(value);
            },
          ),
          Expanded(
            child: _buildThreadList(askAiState),
          ),
        ],
      ),
    );

    if (isSmallScreen) {
      return content;
    }

    return Expanded(
      flex: 2,
      child: content,
    );
  }

  Widget _buildThreadList(AppState<List<Thread>> askAiState) {
    // Show shimmer during initial loading
    if (askAiState.status == AppStatus.loading &&
        (askAiState.data?.isEmpty ?? true)) {
      return const ThreadListShimmer(itemCount: 8);
    }

    // Show error state
    if (askAiState.status == AppStatus.error) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(20.w),
          child: Text(
            askAiState.errorMessage ?? 'Failed to load threads',
            style: TextStyle(
              color: Colors.red,
              fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 16.sp,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    // Show empty state
    if (askAiState.status == AppStatus.empty ||
        (askAiState.data?.isEmpty ?? true)) {
      return Center(
        child: NoDataWidget(
          title: "No threads found",
          subtitle:
              "Try searching again or select a different thread to view the details.",
          width: 250,
          height: 250,
        ),
      );
    }

    // Group threads by time period
    final threadSections =
        ThreadGrouper.groupThreadsByTimePeriod(askAiState.data!);
    final totalItemCount =
        ThreadGrouper.calculateTotalItemCount(threadSections);

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(horizontal: 20),
      itemCount: totalItemCount + (askAiState.hasMore ? 1 : 0),
      // Add 1 for loading indicator
      itemBuilder: (context, index) {
        // Show loading indicator at the bottom for pagination
        if (index >= totalItemCount) {
          return askAiState.status == AppStatus.loadingMore
              ? Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  child: Center(
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      //child: const CircularProgressIndicator(strokeWidth: 2),
                      child: LoadingAnimationWidget.fallingDot(
                        color: Colors.black,
                        size: 50.0,
                      ),
                    ),
                  ),
                )
              : const SizedBox.shrink();
        }

        final item = ThreadGrouper.getItemAtIndex(threadSections, index);

        if (item is ThreadSection) {
          // Render section header
          return _buildSectionHeader(item.title);
        } else if (item is Thread) {
          // Render thread item
          return _buildThreadItem(item, index);
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.only(top: 24.h, bottom: 12.h),
      child: Text(
        title,
        style: context.theme.textTheme.bodyMedium!.copyWith(
          color: AppColors.textGreyColor,
          fontWeight: FontWeight.w400,
          fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 22.sp,
        ),
      ),
    );
  }

  Widget _buildThreadItem(Thread thread, int index) {
    final isSelected = selectedIndex == index;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedIndex = index;
        });
        // Call the callback to notify parent about thread selection
        widget.onThreadSelected?.call(thread);
        if (MediaQuery.of(context).size.width <= 1100) {
          Navigator.of(context).pop();
        }
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 2.h),
        padding: EdgeInsets.symmetric(vertical: 1, horizontal: 10),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color.fromARGB(255, 68, 68, 68).withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            // Expanded(
            //   child: MouseRegion(
            //     cursor: SystemMouseCursors.click,
            //     child: Text(
            //       thread.title,
            //       style: context.theme.textTheme.bodyMedium!.copyWith(
            //         color: AppColors.blackTextColor,
            //         fontWeight: FontWeight.w400,
            //         fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 22.sp,
            //       ),
            //       maxLines: 2,
            //       overflow: TextOverflow.ellipsis,
            //     ),
            //   ),
            // ),
            Expanded(
              child: Container(
                constraints: const BoxConstraints(minHeight: 10),
                padding: EdgeInsets.zero,
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: Text(
                    thread.title.trim(), // ✅ Remove leading/trailing spaces
                    style: context.theme.textTheme.bodyMedium!.copyWith(
                      color: AppColors.blackTextColor,
                      fontWeight: FontWeight.w400,
                      fontSize: DeviceUtils().isTabletOrIpad(context)
                          ? 14
                          : 18.sp, // ✅ Slightly reduced font
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                  ),
                ),
              ),
            ),

            // Show three dots menu for thread actions
            Visibility(
              visible:
                  DeviceUtils().isTabletOrIpad(context) ? true : isSelected,
              child: PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_horiz,
                  color: AppColors.blackColor,
                  size: DeviceUtils().isTabletOrIpad(context) ? 22 : 30.sp,
                ),
                onSelected: (value) async {
                  // Handle menu actions
                  switch (value) {
                    case 'share':
                      // Handle share thread
                      break;
                    case 'download':
                      // Handle save to cookbook
                      break;
                    case 'rename':
                      // Handle rename thread
                      break;
                    case 'delete':
                      // Handle delete thread
                      await _handleDeleteThread(thread);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  // PopupMenuItem<String>(
                  //   value: 'share',
                  //   child: Row(
                  //     children: [
                  //       SvgPicture.asset(
                  //         AssetsManager.share_recipe,
                  //         height: 40.h,
                  //         width: 40.w,
                  //       ),
                  //       SizedBox(width: 15.w),
                  //       CustomDescText(
                  //         desc: "Share",
                  //         textColor: Colors.black,
                  //         size: 24.sp,
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  // PopupMenuItem<String>(
                  //   value: 'download',
                  //   child: Row(
                  //     children: [
                  //       SvgPicture.asset(
                  //         AssetsManager.download,
                  //         height: 40.h,
                  //         width: 40.w,
                  //       ),
                  //       SizedBox(width: 15.w),
                  //       CustomDescText(
                  //         desc: "Save to Cookbook",
                  //         textColor: Colors.black,
                  //         size: 24.sp,
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  // PopupMenuItem<String>(
                  //   value: 'rename',
                  //   child: Row(
                  //     children: [
                  //       SvgPicture.asset(
                  //         AssetsManager.rename,
                  //         height: 40.h,
                  //         width: 40.w,
                  //       ),
                  //       SizedBox(width: 15.w),
                  //       CustomDescText(
                  //         desc: "Rename",
                  //         textColor: Colors.black,
                  //         size: 24.sp,
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  PopupMenuItem<String>(
                    value: 'delete',
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          AssetsManager.dlt_recipe,
                          height: 40.h,
                          width: 40.w,
                        ),
                        SizedBox(width: 15.w),
                        CustomDescText(
                          desc: "Delete",
                          textColor: AppColors.primaryColor,
                          size: DeviceUtils().isTabletOrIpad(context)
                              ? 14
                              : 24.sp,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
