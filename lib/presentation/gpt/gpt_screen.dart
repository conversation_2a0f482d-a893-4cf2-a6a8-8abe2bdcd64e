import 'dart:async';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/get_ask_ai_response.dart';
import 'package:mastercookai/core/data/models/get_ask_ai_thread_messages.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_drawer.dart';
import 'package:mastercookai/presentation/gpt/Widgets/add_reciepe_dialog.dart';
import 'package:mastercookai/presentation/gpt/sidebar.dart';
import 'package:mastercookai/presentation/gpt/welcome_screen.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_appbar.dart';
import '../../../../core/widgets/custom_searchbar.dart';
import '../../../core/data/models/gpt.dart';
import '../../core/providers/gpt/ask_ai_notifier.dart';
import '../../core/providers/gpt/thread_messages_notifier.dart';
import '../../core/widgets/no_data_widget.dart';
import '../cookbook/widgets/custom_desc_text.dart';
import '../../core/helpers/thread_grouper.dart';
import '../../core/network/app_status.dart';
import '../shimer/thread_list_shimmer.dart';
import '../../core/utils/Utils.dart';
import 'Widgets/build_chat_content.dart';
import 'Widgets/thinking_loader.dart';

final chatProvider =
    StateNotifierProvider<ChatNotifier, List<String>>((ref) => ChatNotifier());

class ChatNotifier extends StateNotifier<List<String>> {
  ChatNotifier() : super([]);

  void sendMessage(String message) {
    if (message.isEmpty) return;
    state = [...state, message, 'This is a response to: $message'];
  }
}

class GptScreen extends ConsumerStatefulWidget {
  const GptScreen({super.key});

  @override
  ConsumerState<GptScreen> createState() => _GptScreenState();
}

class _GptScreenState extends ConsumerState<GptScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // Sample ingredients list
  final List<String> _ingredients = [
    '1 cup butter, divided',
    '1 onion, minced',
    '1 tablespoon minced garlic',
    '1 (15 ounce) can tomato sauce',
    '3 cups heavy cream',
  ];

  // Selected thread state
  Thread? selectedThread;

  // Track if this is a new chat session
  bool isNewChat = true;

  // Track the current thread ID for the conversation
  int? currentThreadId;

  // ScrollController for chat messages
  final ScrollController _chatScrollController = ScrollController();

  // Search controller and state
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  // Message search debounce timer
  Timer? _messageSearchDebounce;

  @override
  void initState() {
    super.initState();
    _chatScrollController.addListener(_chatScrollListener);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await ref.read(askAiNotifierProvider.notifier).getAskAiThreads(
            context: context,
            loadMore: false,
          );
      final extras = GoRouterState.of(context).extra as Map<String, dynamic>?;
      final Thread? passedThread = extras?['threads'] as Thread?;
      final askAiState = ref.read(askAiNotifierProvider);

      // Only select a thread if one was passed from navigation
      if (passedThread != null &&
          askAiState.data != null &&
          askAiState.data!.isNotEmpty) {
        final index =
            askAiState.data!.indexWhere((cb) => cb.id == passedThread.id);
        if (index != -1) {
          // Set selected thread and fetch its messages
          setState(() {
            selectedThread = passedThread;
            isNewChat = false; // Not a new chat when thread is passed
            currentThreadId = passedThread.id; // Set current thread ID
          });

          // Fetch messages for the selected thread
          if (mounted) {
            await ref
                .read(threadMessagesNotifierProvider.notifier)
                .fetchThreadsMessages(
                  context: context,
                  id: passedThread.id,
                  loadMore: false,
                );
          }
        }
      }
      // If no thread is passed, start in new chat mode (isNewChat = true by default)
    });
  }

  @override
  void dispose() {
    _chatScrollController.dispose();
    _searchController.dispose();
    _messageSearchDebounce?.cancel();
    super.dispose();
  }

  // Handle thread selection from sidebar
  void _onThreadSelected(Thread thread) async {
    setState(() {
      selectedThread = thread;
      isNewChat = false; // No longer a new chat when thread is selected
      currentThreadId = thread.id; // Set current thread ID
    });

    // Clear search when switching threads
    _searchController.clear();
    setState(() {
      _isSearching = false;
    });

    // Fetch messages for the selected thread
    if (mounted) {
      await ref
          .read(threadMessagesNotifierProvider.notifier)
          .fetchThreadsMessages(
            context: context,
            id: thread.id,
            loadMore: false,
          );
    }
    if (_scaffoldKey.currentState?.isDrawerOpen ?? false) {
      Navigator.of(context).pop();
    }
  }

  // Handle message search functionality
  void _onMessageSearchChanged(String query) {
    setState(() {
      _isSearching = query.isNotEmpty;
    });

    // Cancel previous debounce timer
    _messageSearchDebounce?.cancel();

    // Set up new debounce timer
    _messageSearchDebounce = Timer(const Duration(milliseconds: 500), () {
      if (mounted && currentThreadId != null) {
        ref.read(threadMessagesNotifierProvider.notifier).searchMessages(
              context: context,
              threadId: currentThreadId!,
              query: query,
            );
      }
    });
  }

  // Chat scroll listener for load more messages
  void _chatScrollListener() {
    // Load more when scrolling to the top (for older messages)
    if (_chatScrollController.position.pixels <= 200) {
      final threadMessagesState = ref.read(threadMessagesNotifierProvider);
      if (threadMessagesState.hasMore &&
          threadMessagesState.status != AppStatus.loadingMore &&
          currentThreadId != null) {
        ref.read(threadMessagesNotifierProvider.notifier).fetchThreadsMessages(
              context: context,
              id: currentThreadId!,
              loadMore: true,
              search: _searchController.text.trim().isEmpty
                  ? null
                  : _searchController.text.trim(),
            );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final askAiState = ref.watch(askAiNotifierProvider);
    final askAiNotifier = ref.read(askAiNotifierProvider.notifier);
    // final formKey = useMemoized(() => GlobalKey<FormState>());

    final threadMessagesState = ref.watch(threadMessagesNotifierProvider);

    // Auto-scroll when askAI response is received
    ref.listen<AppState<List<Thread>>>(askAiNotifierProvider, (previous, next) {
      if (previous?.status == AppStatus.creating &&
          next.status == AppStatus.createSuccess) {
        // Response received, scroll to bottom to show the new message
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_chatScrollController.hasClients) {
            _chatScrollController.animateTo(
              _chatScrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }
    });
    final messageController = TextEditingController();
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width <= 1100;
    return Scaffold(
      key: _scaffoldKey,
      drawer: isSmallScreen
          ? CustomDrawer(
              title: 'Threads',
              state: askAiState,
              buildContent: (state) => Sidebar(
                onThreadSelected: _onThreadSelected,
              ),
            )
          : null,
      appBar: CustomAppBar(
        title: "Recipe GPT",
        showDrawerIcon: isSmallScreen,
        actions: [
          // if (isSmallScreen)
          //   Padding(
          //     padding: const EdgeInsets.only(right: 8.0),
          //     child: IconButton(
          //       icon: Image.asset(
          //         AssetsManager.list_icon,
          //         width: 24,
          //         height: 24,
          //       ),
          //       onPressed: () {
          //         _scaffoldKey.currentState?.openDrawer();
          //       },
          //       tooltip: 'Threads',
          //     ),
          //   ),
          GestureDetector(
            onTap: () {
              // Start a new chat session
              setState(() {
                selectedThread = null;
                isNewChat = true;
                currentThreadId = null; // Reset current thread ID
              });

              // Clear search when starting new chat
              _searchController.clear();
              setState(() {
                _isSearching = false;
              });

              // Clear the thread messages
              ref.read(threadMessagesNotifierProvider.notifier).clearMessages();
            },
            child: Row(
              children: [
                SvgPicture.asset(AssetsManager.gpt_edit),
                SizedBox(width: 8.w),
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: Text('New Chat',
                      style: context.theme.textTheme.bodyMedium!.copyWith(
                          color: AppColors.blackTextColor,
                          fontWeight: FontWeight.w400,
                          fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 25.sp)),
                ),
              ],
            ),
          ),
          SizedBox(width: 12.w),
           if (isSmallScreen && !isNewChat)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return ScaleTransition(scale: animation, child: child);
                },
                child: _isSearching
                    ? CustomSearchBar(
                        width: 220,
                        height: 54.h,
                        controller: _searchController,
                        onChanged: _onMessageSearchChanged,
                        onClear: () {
                          setState(() {
                            _isSearching = false;
                          });
                        },
                      )
                    : IconButton(
                        icon: Icon(
                          Icons.search,
                          size: 24,
                          color: const Color.fromARGB(255, 147, 147, 147),
                        ),
                        onPressed: () {
                          setState(() {
                            _isSearching = true;
                          });
                        },
                        tooltip: 'Search Recipes',
                      ),
              ),
            ),
           if (!isSmallScreen && !isNewChat)
          CustomSearchBar(
            controller: _searchController,
            hintText: "Search messages",
            width: DeviceUtils().isTabletOrIpad(context) ? 240 : 400.w,
            onChanged: _onMessageSearchChanged,
          )
        ],
      ),
      body: Stack(
        fit: StackFit.expand,
        children: [
          Image.asset(
            AssetsManager.background_img,
            fit: BoxFit.cover,
          ),
          LayoutBuilder(
            builder: (context, constraints) {
              final isSmallScreen = constraints.maxWidth <= 1100;
              return Row(
                children: [
                  if (!isSmallScreen)
                    Sidebar(
                      onThreadSelected: _onThreadSelected,
                    ),
                  Expanded(
                    flex: isSmallScreen ? 1 : 7,
                    child: Container(
                      margin: EdgeInsets.symmetric(
                          horizontal: screenSize.width * 0.04,
                          vertical: screenSize.height * 0.05),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[200]!),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          buildChatContent(threadMessagesState, _chatScrollController, ref, isNewChat, context),
                          Container(
                            margin: EdgeInsets.symmetric(
                                horizontal: 45.w, vertical: 35.h),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey[200]!),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.09),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Container(
                              margin: EdgeInsets.symmetric(
                                  horizontal: 20.w, vertical: 10.h),
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: TextFormField(
                                          style: context
                                              .theme.textTheme.labelMedium!
                                              .copyWith(fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 24.sp),
                                          controller: messageController,
                                          decoration: InputDecoration(
                                            filled: false,
                                            // ensure this is false
                                            fillColor: Colors.transparent,
                                            hintText:
                                                'Give cake Recipe with image',
                                            hintStyle: context.theme
                                                .inputDecorationTheme.hintStyle!
                                                .copyWith(
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 24.sp),
                                            errorStyle: context
                                                .theme
                                                .inputDecorationTheme
                                                .errorStyle,
                                            border: InputBorder.none,
                                            focusedBorder: InputBorder.none,
                                            enabledBorder: InputBorder.none,
                                            disabledBorder: InputBorder.none,
                                            isDense: true,
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 12,
                                                    vertical: 14),
                                          ),
                                          inputFormatters: [
                                            FilteringTextInputFormatter.allow(
                                                RegExp(r'[a-zA-Z0-9\s,]')),
                                          ],
                                          onFieldSubmitted: (value) {
                                            if (value.trim().isEmpty) return;

                                            // Determine thread ID to use:
                                            // - For new chat: use 0 (creates new thread)
                                            // - For existing conversation: use currentThreadId or selectedThread?.id
                                            final threadIdToUse = isNewChat
                                                ? 0
                                                : (currentThreadId ??
                                                    selectedThread?.id);

                                            askAiNotifier.askAi(
                                                context, threadIdToUse, value,
                                                onResponse: (threadData) {
                                              // Always update currentThreadId from response
                                              setState(() {
                                                currentThreadId = threadData.id;
                                                isNewChat = false; // Set to false after we have the thread ID
                                              });
                                            }, onNewThreadCreated:
                                                    (threadData) {
                                              // When a new thread is created, find and select it
                                              final askAiState = ref
                                                  .read(askAiNotifierProvider);
                                              if (askAiState.data != null &&
                                                  askAiState.data!.isNotEmpty) {
                                                // Find the thread that matches the new thread data
                                                final newThread =
                                                    askAiState.data!.firstWhere(
                                                  (thread) =>
                                                      thread.id ==
                                                      threadData.id,
                                                  orElse: () =>
                                                      askAiState.data!.first,
                                                );
                                                setState(() {
                                                  selectedThread = newThread;
                                                });
                                              }
                                            });
                                            messageController.clear();
                                          },
                                          keyboardType: TextInputType.text,
                                        ),
                                      ),
                                      SizedBox(width: 8.w),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                  Row(
                                    children: [
                                      Visibility(
                                        visible: false,
                                        child: Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 20.w, vertical: 10.h),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            border: Border.all(
                                                color: Colors.grey[200]!),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black
                                                    .withOpacity(0.05),
                                                blurRadius: 10,
                                                offset: const Offset(0, 4),
                                              ),
                                            ],
                                          ),
                                          child: Row(
                                            children: [
                                              SvgPicture.asset(
                                                  AssetsManager.think,
                                                  width: 35.w,
                                                  height: 35.h),
                                              SizedBox(
                                                width: 10.w,
                                              ),
                                              Text("Think before responding",
                                                  style: context.theme.textTheme
                                                      .bodyMedium!
                                                      .copyWith(
                                                          color: AppColors
                                                              .primaryGreyColor,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 24.sp))
                                            ],
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        width: 20.w,
                                      ),
                                      Visibility(
                                          visible: false,
                                          child: Container(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 20.w,
                                                vertical: 10.h),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              border: Border.all(
                                                  color: Colors.grey[200]!),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withOpacity(0.05),
                                                  blurRadius: 10,
                                                  offset: const Offset(0, 4),
                                                ),
                                              ],
                                            ),
                                            child: Row(
                                              children: [
                                                SvgPicture.asset(
                                                    AssetsManager.web_search,
                                                    width: 35.w,
                                                    height: 35.h),
                                                SizedBox(
                                                  width: 10.w,
                                                ),
                                                Text("Search the web",
                                                    style: context.theme
                                                        .textTheme.bodyMedium!
                                                        .copyWith(
                                                            color: AppColors
                                                                .primaryGreyColor,
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 24.sp))
                                              ],
                                            ),
                                          )),
                                      Spacer(),
                                      GestureDetector(
                                        child: Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 10.w, vertical: 10.h),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            border: Border.all(
                                                color: Colors.grey[200]!),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black
                                                    .withValues(alpha: 0.05),
                                                blurRadius: 10,
                                                offset: const Offset(0, 4),
                                              ),
                                            ],
                                          ),
                                          child: SvgPicture.asset(
                                              AssetsManager.ic_send_msg,
                                              width: 35.w,
                                              height: 35.h),
                                        ),
                                        onTap: () {
                                          if (messageController.text
                                              .trim()
                                              .isEmpty) return;

                                          // Determine thread ID to use:
                                          // - For new chat: use 0 (creates new thread)
                                          // - For existing conversation: use currentThreadId or selectedThread?.id
                                          final threadIdToUse = isNewChat
                                              ? 0
                                              : (currentThreadId ??
                                                  selectedThread?.id);

                                          askAiNotifier.askAi(
                                              context,
                                              threadIdToUse,
                                              messageController.text.toString(),
                                              onResponse: (threadData) {
                                            // Always update currentThreadId from response
                                            setState(() {
                                              currentThreadId = threadData.id;
                                              isNewChat =
                                                  false; // Set to false after we have the thread ID
                                            });
                                          }, onNewThreadCreated: (threadData) {
                                            // When a new thread is created, find and select it
                                            final askAiState =
                                                ref.read(askAiNotifierProvider);
                                            if (askAiState.data != null &&
                                                askAiState.data!.isNotEmpty) {
                                              // Find the thread that matches the new thread data
                                              final newThread =
                                                  askAiState.data!.firstWhere(
                                                (thread) =>
                                                    thread.id == threadData.id,
                                                orElse: () =>
                                                    askAiState.data!.first,
                                              );
                                              setState(() {
                                                selectedThread = newThread;
                                              });
                                            }
                                          });

                                          // Auto-scroll to bottom to show thinking loader
                                          Future.delayed(
                                              const Duration(milliseconds: 100),
                                              () {
                                            if (_chatScrollController
                                                .hasClients) {
                                              _chatScrollController.animateTo(
                                                _chatScrollController
                                                    .position.maxScrollExtent,
                                                duration: const Duration(
                                                    milliseconds: 300),
                                                curve: Curves.easeOut,
                                              );
                                            }
                                          });

                                          // ref
                                          //     .read(chatProvider.notifier)
                                          //     .sendMessage(
                                          //         messageController.text);
                                          messageController.clear();
                                        },
                                      ),
                                      SizedBox(width: 20.w),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
