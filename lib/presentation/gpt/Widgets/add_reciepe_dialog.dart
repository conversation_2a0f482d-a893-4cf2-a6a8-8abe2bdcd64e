 // RecipeDetails? _clipperResponse; // Store RecipeDetails

import 'dart:async';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/providers/cookbook_notifier.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';

import '../../../app/imports/packages_imports.dart';
import '../../../core/network/app_status.dart';
import '../../../core/utils/Utils.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/clipper_drop_dpwn.dart';
import '../../cookbook/widgets/CookbookDialog.dart';

final cookbookNameProvider = StateProvider<String>((ref) => '');
final isCookbookCreatedProvider = StateProvider<bool>((ref) => false);
final importRecipeLoadingProvider = StateProvider<bool>((ref) => false);

class AddReciepeDialog extends HookConsumerWidget {
  final RecipeDetails? recipeDetails;

  const AddReciepeDialog({super.key, this.recipeDetails});

  // Regular expression for basic URL validation
  static final RegExp _urlRegExp = RegExp(
    r'^(https?:\/\/)?' // Optional http or https
    r'((([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,})|' // Domain name (e.g., example.com)
    r'(localhost))' // or localhost
    r'(:\d+)?' // Optional port
    r'(\/[^\s]*)?$', // Optional path
    caseSensitive: false,
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final TextEditingController urlController = useTextEditingController();
    final TextEditingController descriptionController =
        useTextEditingController();
    final isDropdownOpen = useState<bool>(false);
    final isUrlValid = useState<bool>(true);
    final isUrlEmpty = useState<bool>(true);
    final selectedCookbook = useState<Cookbook?>(null); // Use reactive state for selectedCookbook

    // Fetch cookbooks on dialog open and set clipper response if recipeDetails exists
    useEffect(() {
      // Set the clipper response in cookbook notifier if recipeDetails is provided
      if (recipeDetails != null) {
        ref.read(cookbookNotifierProvider.notifier).setClipperResponse(recipeDetails!);
      }

      // ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
      //   context: context,
      //   loadMore: false,
      // );
      return null;
    }, []);

    final cookbookState = ref.watch(cookbookNotifierProvider);
    final isImportLoading = ref.watch(importRecipeLoadingProvider);

    // Validate URL on change
    void validateUrl(String value) {
      isUrlEmpty.value = value.trim().isEmpty;
      isUrlValid.value =
          value.trim().isEmpty || _urlRegExp.hasMatch(value.trim());
    }


    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      elevation: 10,
      backgroundColor: Colors.white,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Container(
          color: Colors.white,
          padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 20.h),
          width: DeviceUtils().isTabletOrIpad(context) ? 500 : 800.w,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  icon: SvgPicture.asset(
                    AssetsManager.cross,
                    height: 40.h,
                    width: 40.w,
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Save Recipe",
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: AppColors.primaryGreyColor,
                          fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 26,
                          fontWeight: FontWeight.w700,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              SizedBox(height: 50.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 50.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Select Your Cookbook",
                          style:
                              Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    color: AppColors.primaryGreyColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w600,
                                  ),
                        ),
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () {
                              if (!isDropdownOpen.value) {
                                showCookbookDialog(context, ref);
                              }
                            },
                            child: Text(
                              "+ Add New CookBook",
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(
                                    color: AppColors.primaryGreyColor,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.h),
                    ClipperDropDpwn(
                      hint: "Search Cookbook",
                      selectedValue: selectedCookbook.value,
                      items: cookbookState.data ?? [],
                      cookbookState: cookbookState,
                      onChanged: (val) {
                        selectedCookbook.value = val; // Update reactive state
                        print("Selected Cookbook: ${val?.id} - ${val?.name}");
                      },
                      onOpenStateChanged: (isOpen) {
                        isDropdownOpen.value = isOpen;
                      },
                      icon: SvgPicture.asset(
                        AssetsManager.search,
                        height: 20.h,
                        width: 20.w,
                      ),
                    ),
                    SizedBox(height: 20.h),
                    Text(
                      "Add a Note (Optional)",
                      style: context.theme.textTheme.displaySmall!.copyWith(
                        color: AppColors.primaryGreyColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 20.h),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                            color: AppColors.lightestGreyColor, width: 1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TextField(
                        style: context.theme.textTheme.displaySmall!.copyWith(
                          color: AppColors.primaryGreyColor,
                          fontWeight: FontWeight.w400,
                          fontSize: 16,
                        ),
                        controller: descriptionController,
                        decoration: InputDecoration(
                          filled: context.theme.inputDecorationTheme.filled,
                          fillColor:
                              context.theme.inputDecorationTheme.fillColor,
                          hintStyle: context
                              .theme.inputDecorationTheme.hintStyle
                              ?.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ),
                          errorStyle:
                              context.theme.inputDecorationTheme.errorStyle,
                          hintText:
                              "E.g., reduce salt, try with tofu instead of chicken...",
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 5,
                      ),
                    ),
                    SizedBox(height: 40.h),
                    Center(
                      child: CustomButton(
                        text: 'Add Recipe',
                        height: 30,
                        isLoading: false,
                        width: 250,
                        fontSize: DeviceUtils().isTabletOrIpad(context)
                            ? 16
                            : responsiveFont(18),
                        onPressed: () async {
                          if (selectedCookbook.value == null) {
                            Utils().showFlushbar(
                              context,
                              message: 'Please select a cookbook',
                              isError: true,
                            );
                            return;
                          }

                          var description = descriptionController.text.trim();

                          // Clear controllers on manual add
                          Navigator.pop(context);

                          await context.push(
                            '/cookbook/cookbookDetail/addRecipe',
                            extra: {
                              'selectedCookbook': selectedCookbook.value,
                              'callFromClipper': recipeDetails != null,
                              'clipperNote': description,
                              'recipeDetails': recipeDetails,
                            },
                          );
                        },
                      ),
                    ),
                    SizedBox(height: 24.h),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

void showCookbookDialog(BuildContext context, WidgetRef ref) {
  ref.read(cookbookNotifierProvider.notifier).resetToIdle();
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (_) => CookbookDialog(
      title: "Create new cookbook",
      hintText: "Cookbook name",
      successText: "Cookbook created\nsuccessfully.",
      buttonText: "Create now",
      successButtonText: "Go to Cookbooks",
    ),
  );
}
