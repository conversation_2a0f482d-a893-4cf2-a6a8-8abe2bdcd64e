import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/categories_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/presentation/recipe/subview/add_ingredent.dart';
import 'package:mastercookai/presentation/recipe/subview/media_picker_grid.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_author_widget.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_directions_widegts.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_ingredent_textfield.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_notes_widget.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_serving_ideas_widget.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_update_info.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_wine_widgets.dart';
import 'package:mastercookai/presentation/recipe/tab/tab_add_recipe.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../app/theme/colors.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_loading.dart';
import '../../../../core/widgets/custom_searchbar.dart';
import '../../../../core/widgets/custom_drawer.dart';
import '../../../core/data/models/cookbook.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/data/request_query/create_recipe_request.dart';
import '../../../core/providers/cookbook_notifier.dart';
import '../../../core/providers/recipe_notifier.dart';
import '../../../core/providers/cuisines_notifier.dart';
import '../../../core/providers/recipe/author_provider.dart';
import '../../../core/providers/recipe/directions_provider.dart';
import '../../../core/providers/recipe/ingrident_provider.dart';
import '../../../core/providers/recipe/media_provider.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../../../core/providers/recipe/notes_provider.dart';
import '../../core/utils/device_utils.dart';
import '../cookbook/widgets/cookbook_details_card.dart';
import '../cookbook/widgets/custom_title_text.dart';
import 'subview/build_add_recipe_metadata.dart';

class AddRecipeScreen extends ConsumerStatefulWidget {
  const AddRecipeScreen(
      {super.key,
      this.selectedCookbook,
      this.callFromClipper,
      this.clipperNote,
      this.recipeDetails});

  final Cookbook? selectedCookbook;
  final bool? callFromClipper;
  final String? clipperNote;
  final RecipeDetails? recipeDetails;

  @override
  ConsumerState<AddRecipeScreen> createState() => _AddRecipeScreenState();
}

class _AddRecipeScreenState extends ConsumerState<AddRecipeScreen> {
  late Cookbook? selectedCookbook;
  TextEditingController recipeNameController = TextEditingController();
  TextEditingController recipeDescController = TextEditingController();
  final TextEditingController searchController = TextEditingController();
  bool _isLoading = true;
  RecipeDetails? recipeDetails;

  @override
  void initState() {
    super.initState();
    selectedCookbook = widget.selectedCookbook;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateRecipeDetails();
      setState(() => _isLoading = false);
    });
  }

  @override
  void didUpdateWidget(covariant AddRecipeScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.recipeDetails != oldWidget.recipeDetails) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateRecipeDetails();
      });
    }
  }

  void _updateRecipeDetails() {
    // Reset loading state
    ref.read(loadingProvider.notifier).state = false;

    recipeDetails = widget.recipeDetails;
    if ((widget.callFromClipper ?? false) && recipeDetails != null) {
      // Log all fields for debugging
      print("RecipeDetails: ${recipeDetails?.toJson()}");

      // Initialize controllers
      recipeNameController.text = recipeDetails?.name ?? '';
      recipeDescController.text =
          (recipeDetails?.description?.isEmpty ?? false)
              ? widget.clipperNote ?? ''
              : recipeDetails?.description ?? '';

      // Initialize providers
      // Ingredients
      ref.read(ingredientsProvider.notifier)
        ..clearIngredients()
        ..updateIngredients(recipeDetails?.ingredients ?? []);

      // Directions
      ref.read(directionStepsProvider.notifier).setDirections(
            recipeDetails?.directions ?? [],
          );
      final mediaList =
          List<RecipeMedia>.from(recipeDetails?.recipeMedia ?? []);

      // Metadata
      ref.read(recipeMetadataProvider.notifier).set(
          name: recipeDetails?.name,
          description: recipeDetails?.description,
          ingredients: recipeDetails?.ingredients,
          //  nutritionFacts: recipeDetails?.nutritionInfo,
          author: recipeDetails?.author,
          authorMediaUrl: recipeDetails?.authorMediaUrl,
          copyright: recipeDetails?.copyright,
          source: recipeDetails?.source,
          directions: recipeDetails?.directions,
          servingIdeas: recipeDetails?.servingIdeas,
          wine: recipeDetails?.wine,
          recipeMedia: recipeDetails?.recipeMedia,
          categoryId: recipeDetails?.categoryId ?? null,
          cuisineId: recipeDetails?.cuisineId ?? null,
          yieldValue: recipeDetails?.yield,
          // Changed from yieldUnit to yield
          servings: recipeDetails?.servings,
          prepTime: recipeDetails?.prepTime,
          cookTime: recipeDetails?.cookTime,
          totalTime: recipeDetails?.totalTime,
          wineDesc: recipeDetails?.wine,
          AiImageUrl: recipeDetails?.aiImageUrl,
          AiMessageId: recipeDetails?.aiMessageId?.toString());

      // Author
      ref.read(authorProvider.notifier)
        ..updateAuthorName(recipeDetails?.author ?? '')
        ..updateSource(recipeDetails?.source ?? '')
        ..updateCopyright(recipeDetails?.copyright ?? '');

      // Notes
      ref
          .read(notesProvider.notifier)
          .setNote(recipeDetails?.notes ?? widget.clipperNote);

      if (recipeDetails?.aiImageUrl?.isNotEmpty == true) {
        mediaList.insert(
          0,
          RecipeMedia(
            mediaUrl: '',
          ),
        );
        mediaList.insert(
          1,
          RecipeMedia(
            mediaUrl: recipeDetails?.aiImageUrl!,
          ),
        );
      }

      ref.read(mediaFilesProvider.notifier).set(mediaList);

      // Media Files
      ref
          .read(mediaFilesProvider.notifier)
          .updateMedia(mediaList, recipeDetails?.coverMediaIndex ?? 0);
    } else {
      // Reset providers to default state if recipeDetails is null
      ref.read(ingredientsProvider.notifier).resetWithDefaults();
      ref.read(directionStepsProvider.notifier).resetWithDefaults();
      ref.read(recipeMetadataProvider.notifier).reset();
      ref.read(authorProvider.notifier).reset();
      ref.read(notesProvider.notifier).reset();
      ref.read(mediaFilesProvider.notifier).clear();
      recipeNameController.text = '';
      recipeDescController.text = '';
    }
  }

  @override
  void dispose() {
    recipeNameController.dispose();
    recipeDescController.dispose();
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final notifier = ref.read(recipeNotifierProvider.notifier);
    final isLoading = ref.watch(loadingProvider);

    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    // Listen to recipeNotifierProvider changes to handle side effects
    ref.listen<AppState>(recipeNotifierProvider, (previous, next) {
      if (next.status == AppStatus.loading) {
        ref.read(loadingProvider.notifier).state = true;
      } else if (next.status == AppStatus.success) {
        ref.read(loadingProvider.notifier).state = false;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!mounted) return;
          Utils().showFlushbar(
            context,
            message: 'Recipe saved successfully!',
            isError: false,
            onDismissed: () {
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  Navigator.of(context).pop();
                }
              });
            },
          );
        });
      } else if (next.status == AppStatus.error) {
        ref.read(loadingProvider.notifier).state = false;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!mounted) return;
          Utils().showFlushbar(
            context,
            message: next.errorMessage ?? 'Failed to save recipe',
            isError: true,
          );
        });
      }
    });

    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 1200;

    final cookbookState = ref.watch(cookbookNotifierProvider);

    return Scaffold(
      drawer: isSmallScreen
          ? CustomDrawer(
              title: 'Cookbooks',
              state: cookbookState,
              buildContent: (dynamic) => _buildCookbookList(),
            )
          : null,
      appBar: CustomAppBar(
        title: 'Add Recipe',
        showDrawerIcon: isSmallScreen,
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomButton(
              isDisabled: isLoading,
              text: "Save Changes",
              onPressed: () => onSavePressed(context, ref),
              fontSize: 12,
              width: 120,
            ),
          )
        ],
      ),
      body: Stack(
        children: [
          buildUI(notifier, isSmallScreen),
          if (isLoading)
            const Positioned.fill(child: Center(child: CustomLoading())),
        ],
      ),
    );
  }

  Widget _buildCookbookList() {
    return Column(
      children: [
        SizedBox(height: 20.h),
        CustomSearchBar(
          controller: searchController,
          width: MediaQuery.of(context).size.width,
        ),
        Expanded(
          child: ListView(
            padding: const EdgeInsets.all(12),
            children: [
              if (selectedCookbook != null)
                CookbookDetailCard(
                  cookbook: selectedCookbook!,
                  isSelected: true,
                  onMenuItemSelected: (String _) {},
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildUI(RecipeNotifier notifier, bool isSmallScreen) {
    if (selectedCookbook == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final mainCard = Container(
      decoration: BoxDecoration(
        color: AppColors.secondaryColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.only(right: 30.w, left: 30.h, top: 30.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            IngredientTextField(
              hintText: 'Recipe Name',
              controller: recipeNameController,
              maxLines: 1,
              height: 70.h,
              maxLength: 100,
            ),
            SizedBox(height: 16.h),
            IngredientTextField(
              hintText: 'Recipe Description',
              controller: recipeDescController,
              maxLines: 7,
              height: 200.h,
            ),
            //_buildRecipeHeader(context),
            SizedBox(height: 30.h),
            _buildMainContent(isSmallScreen),
            SizedBox(height: 40.h),
            CustomDirectionsWidgets(
                isCallFromEdit: false, callFromClipper: widget.callFromClipper),
            SizedBox(height: 40.h),
            CustomServingWidget(),
            SizedBox(height: 40.h),
            CustomWineWidget(),
            SizedBox(height: 40.h),
          ],
        ),
      ),
    );

    final ingredientsCard = _buildIngredientsSection(context);
    final authorCard = _buildAuthorSection();
    final notesCard = _buildNotesSection();

    return Stack(
      fit: StackFit.expand,
      children: [
        Image.asset(
          AssetsManager.background_img,
          fit: BoxFit.cover,
        ),
        getDeviceType(context) == DeviceType.tablet
            ? TabAddRecipe(
                recipeNameController: recipeNameController,
                recipeDescController: recipeDescController,
                callFromClipper: widget.callFromClipper)
            : Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 3,
                    child: Container(
                      color: AppColors.secondaryColor,
                      child: _buildCookbookList(),
                    ),
                  ),
                  Expanded(
                    flex: 6,
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: 20.w, right: 20.w, top: 30.h, bottom: 30.h),
                        child: mainCard,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: SingleChildScrollView(
                      hitTestBehavior: HitTestBehavior.translucent,
                      child: Padding(
                        padding: EdgeInsets.only(
                            right: 20.w, top: 30.h, bottom: 30.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ingredientsCard,
                            SizedBox(height: 30.h),
                            authorCard,
                            SizedBox(height: 30.h),
                            notesCard,
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ],
    );
  }

  Widget _buildMainContent(bool isSmallScreen) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 3,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: MediaPickerGrid(
              recipeThumbnailFileUrl: '',
              recipeMedia: ref.watch(mediaFilesProvider).mediaFiles,
            ),
          ),
        ),
        SizedBox(width: 30.w),
        Expanded(
          flex: 2,
          child: buildAddRecipeMetadata(ref: ref),
        ),
      ],
    );
  }

  void onSavePressed(BuildContext context, WidgetRef ref) async {
    if (selectedCookbook == null) {
      Utils().showFlushbar(context,
          message: 'No cookbook selected', isError: true);
      return;
    }

    final metadata = ref.read(recipeMetadataProvider);
    final authorData = ref.read(authorProvider);
    final ingredients = ref.read(ingredientsProvider);
    final mediaFiles = ref.read(mediaFilesProvider);
    final directionNotifier = ref.read(directionStepsProvider.notifier);
    final directionJson = directionNotifier.directionsJson;
    final directionMediaFiles = directionNotifier.mediaFiles;
    final notes = ref.read(notesProvider);

    if (recipeNameController.text.trimRight().isEmpty) {
      Utils().showFlushbar(context,
          message: 'Please enter a recipe name', isError: true);
      return;
    }
    if (metadata.categoryId == null || metadata.categoryId == 0) {
      Utils().showFlushbar(context,
          message: 'Please select a category', isError: true);
      return;
    }
    if (metadata.cuisineId == null || metadata.cuisineId == 0) {
      Utils().showFlushbar(context,
          message: 'Please select a cuisine', isError: true);
      return;
    }

    final selectedMediaFiles = mediaFiles.mediaFiles
        .map((media) => media.mediaFile)
        .whereType<File>()
        .toList();
    final mediaUrl = mediaFiles.mediaFiles
        .map((media) => media.mediaUrl)
        .firstWhere((url) => url != null && url.isNotEmpty, orElse: () => null);

    final request = CreateRecipeRequest(
        type: '',
        name: recipeNameController.text,
        coverRecipeMediaIndex: mediaFiles.coverIndex,
        description: recipeDescController.text,
        categoryId: metadata.categoryId!,
        cuisineId: metadata.cuisineId!,
        yieldValue: metadata.yieldValue ?? '',
        servings: metadata.servings,
        prepTime: metadata.prepTime ?? '',
        cookTime: metadata.cookTime ?? '',
        totalTime: metadata.totalTime ?? '',
        recipeMediaFiles:
            selectedMediaFiles.isEmpty ? null : selectedMediaFiles,
        directionsJson: directionJson ?? '',
        directionMediaFiles:
            directionMediaFiles!.isEmpty ? null : directionMediaFiles,
        servingIdeas: metadata.servingIdeas ?? '',
        wine: metadata.wineDesc ?? '',
        ingredients: ingredients.isEmpty ? null : ingredients,
        authorName: authorData.authorName?.isEmpty ?? true
            ? null
            : authorData.authorName,
        authorSource:
            authorData.source?.isEmpty ?? true ? null : authorData.source,
        authorCopyright:
            authorData.copyright?.isEmpty ?? true ? null : authorData.copyright,
        authorProfileFile: authorData.image,
        notes: notes ?? '',
        AiImageUrl: mediaUrl,
        AiMessageId: metadata.AiMessageId);

    final cookbookId = selectedCookbook!.id;

    final result = await ref.read(recipeNotifierProvider.notifier).createRecipe(
          context: context,
          request: request,
          cookbookId: cookbookId,
        );
    if (result) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        ref.read(loadingProvider.notifier).state = false;
        ref.read(recipeNotifierProvider.notifier).fetchRecipes(
              cookbookId: cookbookId,
              cookbookName: recipeNameController.text,
              cuisineId: 0,
              categoryId: 0,
              reset: true,
              context: context,
              currentPage: 1,
            );
        await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
              context: context,
              loadMore: false,
            );
      });
      if (widget.callFromClipper ?? false) {
        context.go(
            '/cookbook/cookbookDetail?id=${widget.selectedCookbook?.id}&name=${Uri.encodeComponent(widget.selectedCookbook?.name ?? '')}');
      }
    }
  }

  Widget _buildIngredientsSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(24.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CustomeTitleText(title: 'Ingredients'),
          SizedBox(height: 8.h),
          AddIngredientScreen(),
        ],
      ),
    );
  }

  Widget _buildAuthorSection() {
    return Container(
      width: 600.w,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          const CustomeTitleText(title: 'Author info'),
          const SizedBox(height: 20),
          CustomAuthorWidget(),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 8),
          CustomeTitleText(title: 'Notes'),
          CustomNotesWidget(),
        ],
      ),
    );
  }
}
