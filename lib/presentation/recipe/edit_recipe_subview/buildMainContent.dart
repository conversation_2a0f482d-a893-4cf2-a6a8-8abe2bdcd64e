import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/data/models/category_response.dart';
import 'package:mastercookai/core/data/models/cuisines_response.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';

import '../../../app/imports/core_imports.dart';
import '../subview/media_picker_grid.dart';
import 'buildRecipeMetadata.dart';

Widget buildMainContent(
    {required List<RecipeMedia> recipeMedia,
    required recipeThumbnailFileUrl,
    required List<Categories> categoriesList,
    required List<Cuisines> cuisinesList,
    String? category,
    String? cuisine,
    VoidCallback? saveMediaFiles,
    VoidCallback? saveBasicInfo}) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Expanded(
        flex: 3,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              MediaPickerGrid(
                  recipeMedia: recipeMedia,
                  recipeThumbnailFileUrl: recipeThumbnailFileUrl,
                  isCallFromEdit: true,
                  onUpdateData: () => saveMediaFiles!.call()),
            ],
          ),
        ),
      ),
      SizedBox(width: 30.w),
      Expanded(
        flex: 2,
        child: buildRecipeMetadata(
            categoriesList, cuisinesList, category, cuisine,
            onUpdateData: () => saveBasicInfo!.call()),
      ),
    ],
  );
}
