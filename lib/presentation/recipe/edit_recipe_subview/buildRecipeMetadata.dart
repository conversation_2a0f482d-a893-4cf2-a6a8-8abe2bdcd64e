import 'package:mastercookai/core/data/models/category_response.dart';
import 'package:mastercookai/core/data/models/cuisines_response.dart';

import '../../../app/imports/core_imports.dart';
import '../subview/custom_update_info.dart';

Widget buildRecipeMetadata(List<Categories> categories, List<Cuisines> cuisines,
    String? category, String? cuisine,
    {required   Function() onUpdateData}) {
  return Container(
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(color: Colors.grey[200]!),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 10,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: UpdateInfoScreen(categories, cuisines, category, cuisine,
        callFromUpdate: true, onUpdateData: onUpdateData
        //     () {
        //   _saveBasicInfo(context);
        // },
        ),
  );
}
