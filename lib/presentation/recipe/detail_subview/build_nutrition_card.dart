import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../app/imports/core_imports.dart';

Widget buildNutritionCard(BuildContext context, String value, String label) {
  return Container(
    decoration: BoxDecoration(
      color: AppColors.greyCardColor,
      borderRadius: BorderRadius.circular(10),
    ),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          value,
          style: context.theme.textTheme.displaySmall!.copyWith(
            color: AppColors.primaryGreyColor,
            fontWeight: FontWeight.w700,
            fontSize: headingSixFontSize,
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        SizedBox(height: 5.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 2),
          child: Text(
            label,
            style: context.theme.textTheme.bodySmall!.copyWith(
              color: AppColors.textGreyColor,
              fontWeight: FontWeight.w400,
              fontSize: headingSixFontSize,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    ),
  );
}