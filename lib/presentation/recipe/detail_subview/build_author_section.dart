import 'package:flutter_riverpod/src/consumer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/data/models/recipe_response.dart';

import '../../../app/imports/core_imports.dart';
import '../../../core/data/models/category_response.dart';
import '../../../core/data/models/cuisines_response.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/providers/single_recipe_notifier.dart';
import '../../../core/widgets/custom_info_cards.dart';
import '../../../core/widgets/custom_network_image.dart';
import '../../../core/widgets/custom_text.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import '../../cookbook/widgets/edit_button.dart';

Widget buildAuthorSection(
    BuildContext context,
    authorName,
    authorUrl,
    copyRight,
    source,
    RecipeDetails recipeDetail,
    List<Categories>? categoriesList,
    List<Cuisines>? cuisinesList,
    int recipeId,
    int cookbookId,
    List<Recipe> recipesList,
    {required WidgetRef ref}) {
  return Container(
    padding: EdgeInsets.all(24.sp),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CustomeTitleText(
              title: "Author info",
            ),
            Spacer(),
            Visibility(
              visible: authorName.isNotEmpty ||
                  source.isNotEmpty ||
                  copyRight.isNotEmpty,
              child: Align(
                alignment: Alignment.topRight,
                child: EditButton(onPressed: () {
                  ref
                      .read(singleRecipeNotifierProvider.notifier)
                      .goToEditRecipeScreen(
                          context,
                          recipeId,
                          cookbookId,
                          recipesList,
                          recipeDetail,
                          categoriesList!,
                          cuisinesList!);
                }),
              ),
            ),
          ],
        ),
        SizedBox(height: 10.h),
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: authorUrl.isNotEmpty
                  ? SizedBox(
                      height: 160,
                      width: 160,
                      child: ClipOval(
                        child: CustomNetworkImage(
                          fit: BoxFit.cover,
                          imageUrl: authorUrl,
                          errorWidget: Image.asset(
                            AssetsManager.recipe_place_holder,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    )
                  : const CircleAvatar(
                      radius: 80,
                      child: Icon(Icons.person),
                    ),
            ),
            authorName.isEmpty && source.isEmpty && copyRight.isEmpty
                ? CustomInfoCard(
                    title: 'Author not added yet',
                    subheading: 'Credit the creator behind this recipe',
                    actionText: 'Add Author Info',
                    onActionPressed: () {
                      ref
                          .read(singleRecipeNotifierProvider.notifier)
                          .goToEditRecipeScreen(
                              context,
                              recipeId,
                              cookbookId,
                              recipesList,
                              recipeDetail,
                              categoriesList!,
                              cuisinesList!);
                    })
                : Column(
                    children: [
                      SizedBox(height: 10),
                      Center(
                          child: CustomText(
                        text: authorName ?? '',
                        size: 18,
                        weight: FontWeight.w600,
                        color: AppColors.primaryGreyColor,
                      )),
                      SizedBox(height: 8.h),
                      GestureDetector(
                        onTap: () {
                          // Handle URL navigation
                        },
                        child: CustomText(
                          text: source ?? '',
                          align: TextAlign.center,
                          color: AppColors.primaryBorderColor,
                          weight: FontWeight.w400,
                          size: 18,
                        ),
                      ),
                      SizedBox(height: 12.h),
                      CustomText(
                        text: copyRight ?? '',
                        size: 16,
                        color: AppColors.primaryLightTextColor,
                      ),
                    ],
                  ),
            SizedBox(height: 20.h),
          ],
        ),
      ],
    ),
  );
}
