import 'package:flutter_riverpod/src/consumer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../../app/imports/core_imports.dart';
import '../../../core/data/models/category_response.dart';
import '../../../core/data/models/cuisines_response.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/data/models/recipe_response.dart';
import '../../../core/providers/single_recipe_notifier.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/custom_info_cards.dart';
import '../../../core/widgets/custom_text.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import '../../cookbook/widgets/edit_button.dart';

Widget buildIngredientsSection(
    BuildContext context,
    List<Recipe> recipesList,
    int cookbookId,
    int recipeId,
    List<String>? ingredients,
    RecipeDetails recipeDetail,
    List<Categories>? categoriesList,
    List<Cuisines>? cuisinesList,
    {required WidgetRef ref}) {
  return Container(
    padding: EdgeInsets.all(24.sp),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CustomeTitleText(title: 'Ingredients'),
            Spacer(),
            Visibility(
              visible: ingredients?.isNotEmpty??false,
              child: Align(
                alignment: Alignment.topRight,
                child: EditButton(onPressed: () {
                  ref
                      .read(singleRecipeNotifierProvider.notifier)
                      .goToEditRecipeScreen(
                      context,
                      recipeId,
                      cookbookId,
                      recipesList,
                      recipeDetail,
                      categoriesList??[],
                      cuisinesList??[]);
                }),
              ),
            ),
          ],
        ),
        SizedBox(height: 20.h),
        ingredients!.isNotEmpty
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: ingredients.map((ingredient) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: 12),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(AssetsManager.dot,
                            height: 8.sp, width: 8.sp),
                        SizedBox(width: 20.w),
                        Expanded(
                            child: CustomText(
                          text: ingredient,
                          size: 14,
                          color: AppColors.primaryLightTextColor,
                          weight: FontWeight.w400,
                        ))
                      ],
                    ),
                  );
                }).toList(),
              )
            : CustomInfoCard(
                title: 'No ingredients added yet',
                subheading: 'Add all the ingredients you need for this dish.',
                actionText: 'Add Ingredients',
                onActionPressed: () {
                  ref
                      .read(singleRecipeNotifierProvider.notifier)
                      .goToEditRecipeScreen(
                          context,
                          recipeId,
                          cookbookId,
                          recipesList,
                          recipeDetail,
                          categoriesList!,
                          cuisinesList!);
                }),
        SizedBox(height: 20.h),
      ],
    ),
  );
}
