import 'package:flutter/cupertino.dart';
import 'package:mastercookai/core/data/models/category_response.dart';
import 'package:mastercookai/core/data/models/cuisines_response.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/data/models/recipe_response.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../app/theme/colors.dart';
import '../../../core/providers/tab_index_notifier.dart';
import '../../../core/widgets/custom_button.dart';
import '../edit_recipe_subview/buildAuthorSection.dart';
import '../edit_recipe_subview/buildMainContent.dart';
import '../edit_recipe_subview/buildRecipeHeader.dart';
import '../subview/custom_directions_widegts.dart';
import '../subview/custom_serving_ideas_widget.dart';
import '../subview/custom_wine_widgets.dart';
import 'custom_Tabbar.dart';

class TabEditRecipe extends ConsumerStatefulWidget {
  final TextEditingController recipeNameController;
  final TextEditingController recipeDescController;
  final List<RecipeMedia> recipeMedia;
  final String recipeThumbnailFileUrl;
  final List<Categories> categoriesList;
  final List<Cuisines> cuisinesList;

  final Function() saveMediaFiles;
  final Function() saveBasicInfo;
  final Function() saveDirections;
  final Function() saveAuthor;
  final Function() saveOthers;
  final String servingIdeas;
  final String wine;
  final String author;
  final String authorMediaUrl;
  final String copyright;
  final String source;
  final String notes;
  final Widget notesCard;
  final Widget ingredientsCard;
  final int recipeId;
  final int cookbookId;
  final List<Recipe> recipesList;
  final RecipeDetails recipeDetails;

  TabEditRecipe(
      {super.key,
      required this.recipeNameController,
      required this.recipeDescController,
      required this.recipeMedia,
      required this.recipeThumbnailFileUrl,
      required this.categoriesList,
      required this.cuisinesList,
      category,
      cuisine,
      required this.saveMediaFiles,
      required this.saveBasicInfo,
      required this.saveAuthor,
      required this.saveDirections,
      required this.saveOthers,
      required this.servingIdeas,
      required this.wine,
      required this.author,
      required this.authorMediaUrl,
      required this.copyright,
      required this.source,
      required this.notes,
      required this.notesCard,
      required this.ingredientsCard,
      required this.recipeId,
      required this.cookbookId,
      required this.recipesList,
      required this.recipeDetails});

  @override
  ConsumerState<TabEditRecipe> createState() => _TabRecipeDetailState();
}

class _TabRecipeDetailState extends ConsumerState<TabEditRecipe> {
  String? selectedMediaUrl;
  dynamic selectedMediaFile; // Adjust type if known
  String? category;
  String? cuisine;

  @override
  void initState() {
    super.initState();
    category = widget.recipeDetails.category ?? (widget.categoriesList.isNotEmpty ? widget.categoriesList.first.name : null);
     cuisine = widget.recipeDetails.cuisine ?? (widget.cuisinesList.isNotEmpty ? widget.cuisinesList.first.name : null);

  }

  @override
  Widget build(BuildContext context) {
    final selectedTabIndex = ref.watch(tabIndexNotifierProvider);
     return Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.secondaryColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: SingleChildScrollView(
            child: Column(
          children: [
            SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: buildRecipeHeader(
                context,
                ref,
                recipeNameController: widget.recipeNameController,
                recipeDescController: widget.recipeDescController,
              ),
            ),
            SizedBox(height: 30.h),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: buildMainContent(
                  recipeMedia: widget.recipeMedia,
                  recipeThumbnailFileUrl: widget.recipeThumbnailFileUrl,
                  categoriesList: widget.categoriesList,
                  cuisinesList: widget.cuisinesList,
                  category: category,
                  cuisine: cuisine,
                  saveMediaFiles: widget.saveMediaFiles,
                  saveBasicInfo: widget.saveBasicInfo),
            ),
            SizedBox(height: 30.h),
            CustomTabBar(
              selectedTabIndex: selectedTabIndex,
              onTabSelected: (index) {
                setState(() {
                  ref.read(tabIndexNotifierProvider.notifier).selectTab(index);

                  // selectedTabIndex = index;
                });
              },
            ),
            SizedBox(height: 20.h),
            _buildTabContent(),
          ],
        )));
  }

  Widget _buildTabContent() {
    switch (ref.read(tabIndexNotifierProvider)) {
      case 0: // Author Recipe
        return widget.ingredientsCard;
      case 1:
      return CustomDirectionsWidgets(
        isCallFromEdit: true,
        callFromClipper: false,
        onUpdateData: () {
          widget.saveDirections();
        },
      );

      case 2:
        return widget.notesCard;
      case 3:
        return buildEditAuthorSection(context, widget.author,
            widget.authorMediaUrl, widget.copyright, widget.source,
            onSaveAuthor: widget.saveAuthor);

      case 4:
        return Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              CustomServingWidget(
                servingIdeas: widget.servingIdeas,
              ),
              SizedBox(height: 40.h),
              CustomWineWidget(
                wine: widget.wine,
              ),
              SizedBox(height: 40.h),
              Center(
                child: CustomButton(
                    text: "Save Changes",
                    fontSize: 14,
                    width: 240,
                    onPressed: widget.saveOthers),
              ),
              SizedBox(height: 40.h),
            ],
          ),
        );
      default:
        return SizedBox.shrink();
    }
  }
}
