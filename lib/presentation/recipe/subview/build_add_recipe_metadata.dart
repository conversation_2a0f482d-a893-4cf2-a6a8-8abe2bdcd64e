import 'package:flutter_riverpod/src/consumer.dart';
import '../../../app/imports/core_imports.dart';
import '../../../core/providers/categories_notifier.dart';
import '../../../core/providers/cuisines_notifier.dart';
import 'custom_update_info.dart';

Widget buildAddRecipeMetadata({required WidgetRef ref}) {
  final categoriesState = ref.watch(categoriesNotifierProvider);
  final cuisinesState = ref.watch(cuisinesNotifierProvider);
  return Container(
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(color: Colors.grey[200]!),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 10,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: UpdateInfoScreen(
      categoriesState.data ?? [],
      cuisinesState.data ?? [],
      'Select Category',
      'Select Cuisine',
      callFromUpdate: false,
    ),
  );
}