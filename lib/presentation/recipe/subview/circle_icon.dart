
import 'package:flutter_svg/svg.dart';
import '../../../app/imports/core_imports.dart';

Widget circleIcon(String? assetName, VoidCallback onTap) {
  if (assetName == null || assetName.isEmpty) {
    // Log error and return a fallback widget
    print('Error: Invalid asset path in _circleIcon');
    return InkWell(
      onTap: onTap,
      child: CircleAvatar(
        backgroundColor: AppColors.lightestGreyColor.withValues(alpha: 0.4),
        radius: 16,
        child:
        Icon(Icons.error, color: Colors.red, size: 16), // Fallback icon
      ),
    );
  }
  return InkWell(
    onTap: onTap,
    child: CircleAvatar(
      backgroundColor: AppColors.lightestGreyColor.withValues(alpha: 0.4),
      radius: 16,
      child: SvgPicture.asset(
        assetName,
        width: 16,
        height: 16,
        placeholderBuilder: (context) =>
            Icon(Icons.error, color: Colors.red, size: 16),
      ),
    ),
  );
}