import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/helpers/media_picker_service.dart';
import 'package:mastercookai/core/providers/recipe/media_provider.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/video_player_widget.dart';
import '../../../core/widgets/image_cropper.dart';
import 'circle_icon.dart';
import 'custom_sub_media_widget.dart';

class MediaPickerGrid extends ConsumerStatefulWidget {
  final List<RecipeMedia>? recipeMedia;
  bool? isCallFromEdit;
  String? recipeThumbnailFileUrl;
  VoidCallback? onUpdateData;

  MediaPickerGrid({
    super.key,
    this.recipeMedia,
    required this.recipeThumbnailFileUrl,
    this.isCallFromEdit = false,
    this.onUpdateData,
  });

  @override
  ConsumerState<MediaPickerGrid> createState() => _MediaPickerGridState();
}

class _MediaPickerGridState extends ConsumerState<MediaPickerGrid> {
  List<RecipeMedia?> mediaFiles = List.generate(9, (_) => null); // 9 items

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      print("Initializing MediaPickerGrid, recipeMedia length: ${widget.recipeThumbnailFileUrl}");
      print("Initializing MediaPickerGrid, recipeMedia length: ${widget.recipeMedia?.length ?? 0}");
      if (widget.recipeMedia != null && widget.recipeMedia!.isNotEmpty) {
        setState(() {
          int targetIndex = 0; // Start at 0 for video
          for (int i = 0; i < widget.recipeMedia!.length && targetIndex < 9; i++) {
            print("ivalue ${widget.recipeMedia![i].mediaType}");
            if (targetIndex == 0 && widget.recipeMedia![i].mediaType == 'VIDEO') {
              mediaFiles[0] = widget.recipeMedia![i];
              targetIndex = 1; // Move to next index for non-video media
              print("Assigned media at index 0: ${widget.recipeMedia![i].toJson()}");
            } else {
             // if (targetIndex == 0) targetIndex = 1; // Skip index 0 if not a video
              if (targetIndex < 9) {
                mediaFiles[targetIndex] = widget.recipeMedia![i];
                print("Assigned media at index $targetIndex: ${widget.recipeMedia![i].toJson()}");
                targetIndex++; // Increment for next available slot
              }
            }
          }
          // Set coverIndex to the first non-null index (excluding 0), default to 1 if no media
          final newCoverIndex = mediaFiles
              .asMap()
              .entries
              .firstWhere(
                (entry) => entry.key != 0 && entry.value != null,
            orElse: () => MapEntry(1, null),
          )
              .key;
          print("Initial coverIndex: $newCoverIndex");
          // Update provider with media files and coverIndex
          ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles, newCoverIndex);
        });
      }
    });
  }

  void _removeMedia(int index) {
    setState(() {
      mediaFiles[index] = null;
      // Update coverIndex if the removed media was the cover
      if (ref.read(mediaFilesProvider).coverIndex == index) {
        final newCoverIndex = mediaFiles
            .asMap()
            .entries
            .firstWhere(
              (entry) => entry.key != 0 && entry.value != null,
          orElse: () => MapEntry(1, null),
        )
            .key;
        ref.read(mediaFilesProvider.notifier).setCoverIndex(newCoverIndex);
      }
    });
    ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles, ref.read(mediaFilesProvider).coverIndex);
  }

  void _setAsCover(int index) {
    if (index != 0) { // Prevent setting cover to index 0 (video)
      setState(() {
        ref.read(mediaFilesProvider.notifier).setCoverIndex(index);
      });
    }
  }

  Widget _buildMediaItem(int index) {
    final media = mediaFiles[index];
    final providerState = ref.watch(mediaFilesProvider);
    final isCover = index == providerState.coverIndex;

    // For index 0: show video if present, else show add video view
    if (index == 0) {
      if (media != null && media.mediaType == 'VIDEO') {
        return Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: isCover ? Colors.red : Colors.transparent,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: VideoPlayerWidget(
                  // Prioritize mediaFile if available (especially in edit mode)
                  mediaFile: media.mediaFile,
                  // Only use mediaUrl if mediaFile is null and not in edit mode
                  mediaUrl: widget.isCallFromEdit == true && media.mediaFile != null
                      ? null
                      : media.mediaUrl,
                  thumbnailPath: widget.recipeThumbnailFileUrl,
                ),
              ),
            ),
            Visibility(
              visible: (media.mediaUrl?.isNotEmpty ?? false) ||
                  (media.mediaFile?.path.isNotEmpty ?? false),
              child: Positioned(
                bottom: 8,
                left: 8,
                right: 8,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    circleIcon(AssetsManager.edit_white, () => _removeMedia(index)),
                    circleIcon(AssetsManager.dlt_white, () {
                      _removeMedia(index);
                      addImage(index);
                    }),
                    // Swipe icon not shown for index 0 (video)
                  ],
                ),
              ),
            ),
          ],
        );
      } else {
        // Show add video view
        return _buildMediaSubImagesItem(0);
      }
    }

    // For other indices: show image if present, else show add image view
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isCover ? Colors.red : Colors.transparent,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(8),
            image: DecorationImage(
              image: media!.mediaFile != null
                  ? FileImage(media.mediaFile!)
                  : NetworkImage(media.mediaUrl!) as ImageProvider,
              fit: BoxFit.cover,
            ),
          ),
        ),
        if (isCover)
          const Positioned(
            top: 4,
            left: 4,
            child: Text(
              "Cover",
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                backgroundColor: Colors.red,
                fontSize: 20,
              ),
            ),
          ),
        Visibility(
          visible: index > 0,
          child: Positioned(
            bottom: 8,
            left: 8,
            right: 8,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                circleIcon(AssetsManager.edit_white, () => _removeMedia(index)),
                circleIcon(AssetsManager.dlt_white, () => addImage(index)),
                Visibility(
                  visible: !isCover, // Hide swipe icon if this is the cover image
                  child: circleIcon(AssetsManager.swipe, () => _setAsCover(index)),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMediaSubImagesItem(int index) {
    return GestureDetector(
      onTap: () async {
        print('Opening picker for index $index');
        final files = await MediaPickerService.pickImages(
          allowMultiple: false,
          videoOnly: index == 0, // Restrict to videos for index 0
          allowVideo: index != 0, // Allow images and videos for other indices
        );

        if (files.isNotEmpty) {
          final file = files.first;
          final isVideo = Utils().isVideo(file.path);

          if (isVideo) {
            // For videos, directly add without cropping
            setState(() {
              mediaFiles[index] = RecipeMedia(mediaFile: file, mediaType: 'VIDEO');
              // If adding media at index 1, set it as cover
              if (index == 1) {
                ref.read(mediaFilesProvider.notifier).setCoverIndex(1);
              }
            });
            ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles, ref.read(mediaFilesProvider).coverIndex);
          } else {
            // For images, navigate to cropper
            if (mounted) {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => ImageCropper(
                    pickedImage: file,
                    showCropPresets: true,
                    showGridLines: false,
                    useDelegate: true,
                    enableFreeformCrop: true,
                    onImageCropped: (File? croppedImageFile) {
                      if (croppedImageFile != null) {
                        setState(() {
                          mediaFiles[index] = RecipeMedia(
                              mediaFile: croppedImageFile, mediaType: 'IMAGE');
                          // If adding media at index 1, set it as cover
                          if (index == 1) {
                            ref.read(mediaFilesProvider.notifier).setCoverIndex(1);
                          }
                        });
                        ref
                            .read(mediaFilesProvider.notifier)
                            .updateMedia(mediaFiles, ref.read(mediaFilesProvider).coverIndex);
                      } else {
                        print("Cropped image is null!");
                      }
                    },
                  ),
                ),
              );
            }
          }
        }
      },
      child: CustomPaint(
        painter: DottedBorderPainter(),
        child: Container(
          height: 120.h,
          alignment: Alignment.center,
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 8.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey.shade300),
              color: Colors.white,
            ),
            child: Padding(
              padding: const EdgeInsets.all(1.0),
              child: TextButton(
                onPressed: () async {
                  await addImage(index);
                },
                child: CustomText(
                  text: index == 0 ? "Add Video" : "Add Image",
                  color: AppColors.primaryLightTextColor,
                  size: 13,
                  weight: FontWeight.w400,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> addImage(int index) async {
    final files = await MediaPickerService.pickImages(
      allowMultiple: false,
      videoOnly: index == 0, // Restrict to videos for index 0
      allowVideo: false, // Allow images only for other indices
    );

    if (files.isNotEmpty) {
      final file = files.first;

      // Check file size for video (index 0)
      if (index == 0) {
        final fileSize = await file.length();
        const maxSize = 150 * 1024 * 1024; // 150 MB in bytes
        if (fileSize > maxSize) {
          if (mounted) {
            Utils().showFlushbar(context,
                message: 'Video file size should not exceed 150MB',
                isError: true);
          }
          return;
        }
        // For videos, directly add without cropping
        setState(() {
          mediaFiles[index] = RecipeMedia(mediaFile: file, mediaType: 'VIDEO');
        });
        ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles, ref.read(mediaFilesProvider).coverIndex);
      } else {
        // For images, navigate to cropper
        if (mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ImageCropper(
                pickedImage: file,
                showCropPresets: true,
                showGridLines: false,
                useDelegate: true,
                enableFreeformCrop: true,
                onImageCropped: (File? croppedImageFile) {
                  if (croppedImageFile != null) {
                    setState(() {
                      mediaFiles[index] = RecipeMedia(mediaFile: croppedImageFile, mediaType: 'IMAGE');
                      // If adding media at index 1, set it as cover
                      if (index == 1) {
                        ref.read(mediaFilesProvider.notifier).setCoverIndex(1);
                      }
                    });
                    ref
                        .read(mediaFilesProvider.notifier)
                        .updateMedia(mediaFiles, ref.read(mediaFilesProvider).coverIndex);
                  } else {
                    print("Cropped image is null!");
                  }
                },
              ),
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final providerState = ref.watch(mediaFilesProvider);
    print("Current coverIndex from provider: ${providerState.coverIndex}");

    return Column(
      children: [
        GridView.builder(
          shrinkWrap: true,
          itemCount: 9,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            childAspectRatio: 1,
          ),
          itemBuilder: (context, index) {
            print("Media at index $index: ${mediaFiles[index]?.toJson() ?? 'null'}");
            if (mediaFiles[index] != null) {
              return _buildMediaItem(index);
            } else {
              return _buildMediaSubImagesItem(index);
            }
          },
        ),
        SizedBox(height: 20.h),
        Visibility(
          visible: widget.isCallFromEdit ?? false,
          child: SizedBox(height: 20.h),
        ),
        Visibility(
          visible: widget.isCallFromEdit ?? false,
          child: CustomButton(
            text: "Save Changes",
            fontSize: 16,
            width: 240,
            onPressed: () {
              widget.onUpdateData?.call();
            },
          ),
        ),
      ],
    );
  }
}