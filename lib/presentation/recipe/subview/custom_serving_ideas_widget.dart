import '../../../../../app/imports/packages_imports.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import 'custom_ingredent_textfield.dart';

class CustomServingWidget extends ConsumerStatefulWidget {
  final String? servingIdeas;
  CustomServingWidget({super.key,   this.servingIdeas});

  @override
  ConsumerState<CustomServingWidget> createState() =>
      _CustomServingWidgetState();
}

class _CustomServingWidgetState extends ConsumerState<CustomServingWidget> {

  late TextEditingController descController;

  @override
  void initState() {
    super.initState();
    // Initialize noteController with value from notesProvider or widget.notes
    descController = TextEditingController(
      text:  widget.servingIdeas ?? '',
    );
  }


  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomeTitleText(title: 'Serving ideas'),
          SizedBox(height: 20.h),
          IngredientTextField(
            onChanged: (val){
              ref.read(recipeMetadataProvider.notifier).updateServingIdeas(val);
            },
            hintText: "Description",
            controller: descController,
            maxLines: 7,
            height: 200.h,
          ),

          // CustomButton(text: "Save changes", onPressed: (){} , width: 210.w, fontSize: 18.sp,),
        ],
      ),
    );
  }
}
