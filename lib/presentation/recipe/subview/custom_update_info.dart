import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/material.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/presentation/recipe/subview/bottom_sheet_time_picker.dart'
    hide AppColors;
import '../../../core/data/models/category_response.dart';
import '../../../core/data/models/cuisines_response.dart';
import '../../../core/network/app_status.dart';
import '../../../core/providers/cuisines_notifier.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_loading.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import '../../../core/widgets/custom_drop_down.dart';
import 'custom_ingredent_textfield.dart';
import 'ingredient_timefield.dart';

class UpdateInfoScreen extends ConsumerStatefulWidget {
  List<Categories> categories;
  List<Cuisines> cuisines;
  String? category;
  String? cuisine;
  bool callFromUpdate;
  VoidCallback? onUpdateData;

  UpdateInfoScreen(
    this.categories,
    this.cuisines,
    this.category,
    this.cuisine, {
    super.key,
    required this.callFromUpdate,
    this.onUpdateData,
  });

  @override
  ConsumerState<UpdateInfoScreen> createState() => _UpdateInfoScreenState();
}

class _UpdateInfoScreenState extends ConsumerState<UpdateInfoScreen> {
  // Text controllers
  final _yieldController = TextEditingController();
  final _servingsController = TextEditingController();
  final _prepTimeController = TextEditingController();
  final _cookTimeController = TextEditingController();
  final _totalTimeController = TextEditingController();

  @override
  void initState() {
    super.initState();

    setDefaultData();
  }

  void setDefaultData() {
    final metadata = ref.read(recipeMetadataProvider);
    _yieldController.text = metadata.yieldValue ?? '';
    _servingsController.text = metadata.servings?.toString() ?? '';
    _prepTimeController.text = metadata.prepTime ?? '';
    _cookTimeController.text = metadata.cookTime ?? '';
    _totalTimeController.text = metadata.totalTime ?? '';

    // Parse existing time values to set hours and minutes variables
    final prepTimeData = _parseTimeString(metadata.prepTime);
    prepHours = prepTimeData['hours']!;
    prepMinutes = prepTimeData['minutes']!;

    final cookTimeData = _parseTimeString(metadata.cookTime);
    cookHours = cookTimeData['hours']!;
    cookMinutes = cookTimeData['minutes']!;
  }

  void reset() {
    _yieldController.text = '';
    _servingsController.text = '';
    _prepTimeController.text = '';
    _cookTimeController.text = '';
    _totalTimeController.text = '';
  }

  @override
  void dispose() {
    _yieldController.dispose();
    _servingsController.dispose();
    _prepTimeController.dispose();
    _cookTimeController.dispose();
    _totalTimeController.dispose();
    super.dispose();
  }

  int prepHours = 0, prepMinutes = 0;
  int cookHours = 0, cookMinutes = 0;

  String _formatTime(int hours, int minutes) {
    return "${hours}h ${minutes}m";
  }

  // Parse time string like "2h 30m" into hours and minutes
  Map<String, int> _parseTimeString(String? timeString) {
    if (timeString == null || timeString.isEmpty) {
      return {'hours': 0, 'minutes': 0};
    }

    // Regular expression to match "Xh Ym" format
    final regex = RegExp(r'(\d+)h\s*(\d+)m');
    final match = regex.firstMatch(timeString);

    if (match != null) {
      return {
        'hours': int.tryParse(match.group(1) ?? '0') ?? 0,
        'minutes': int.tryParse(match.group(2) ?? '0') ?? 0,
      };
    }

    return {'hours': 0, 'minutes': 0};
  }

  String _calculateTotalTime() {
    int totalMinutes =
        (prepHours * 60 + prepMinutes) + (cookHours * 60 + cookMinutes);
    int totalH = totalMinutes ~/ 60;
    int totalM = totalMinutes % 60;

    return _formatTime(totalH, totalM);
  }

  void _updateTotalTimeIfPossible() {
    // Calculate and update total time automatically
    String totalTime = _calculateTotalTime();

    // Update the total time controller and provider
    _totalTimeController.text = totalTime;
    ref.read(recipeMetadataProvider.notifier).updateTotalTime(totalTime);
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomeTitleText(title: 'Update info'),
          SizedBox(height: 20.h),
          CustomDropdown<Categories>(
            label: 'Category',
            value: widget.category,
            options: widget.categories,
            onChanged: (val) {
              setState(() {
                widget.category = val;
              });
              final category = widget.categories.firstWhere(
                (c) => c.name == val,
                orElse: () => widget.categories.isNotEmpty
                    ? widget.categories[0]
                    : Categories(id: 0, name: 'Unknown'),
              );
              ref
                  .read(recipeMetadataProvider.notifier)
                  .updateCategoryId(category.id);
            },
            getDisplayString: (category) => category.name ?? 'Unknown',
          ),
          SizedBox(height: 16.h),
          Consumer(
            builder: (context, ref, child) {
              final cuisinesState = ref.watch(cuisinesNotifierProvider);

              if (cuisinesState.status == AppStatus.loading ||
                  cuisinesState.status == AppStatus.loadingMore) {
                return const CustomLoading();
              }

              if (cuisinesState.status == AppStatus.error) {
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                        'Error: ${cuisinesState.errorMessage ?? 'Failed to load cuisines'}'),
                    TextButton(
                      onPressed: () {
                        ref
                            .read(cuisinesNotifierProvider.notifier)
                            .fetchCuisines(context: context);
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                );
              }

              if (cuisinesState.status == AppStatus.success) {
                final cuisines = cuisinesState.data ?? [];

                if (cuisines.isEmpty) {
                  return const Text('No cuisines available');
                }

                return CustomDropdown<Cuisines>(
                  label: 'Cuisine',
                  value: widget.cuisine,
                  options: cuisines,
                  onChanged: (val) {
                    setState(() {
                      widget.cuisine = val;
                    });
                    final cuisine = cuisines.firstWhere(
                      (c) => c.name == val,
                      orElse: () => cuisines[0],
                    );
                    ref
                        .read(recipeMetadataProvider.notifier)
                        .updateCuisineId(cuisine.id!);
                  },
                  getDisplayString: (cuisine) => cuisine.name ?? 'Unknown',
                );
              }

              return const SizedBox.shrink();
            },
          ),
          SizedBox(height: 16.h),
          _buildTextField(
            'Yield',
            'Amount/Unit',
            _yieldController,
            (val) {
              ref.read(recipeMetadataProvider.notifier).updateYield(val);
            },
            InputFormatType.text,
          ),
          SizedBox(height: 16.h),
          _buildTextField(
            'Servings',
            'Servings',
            _servingsController,
            (val) {
              final servingValue = int.tryParse(val);
              if (servingValue != null) {
                ref
                    .read(recipeMetadataProvider.notifier)
                    .updateServings(servingValue);
              }
            },
            InputFormatType.number,
          ),
          if (DeviceUtils().isTabletOrIpad(context))
            TimePickerFieldBottomSheet(
              label: "Prep time",
              controller: _prepTimeController,
              onTimeSelected: (h, m) {
                // Update prep time variables
                prepHours = h;
                prepMinutes = m;
                // Format and update prep time
                String prepTime = _formatTime(h, m);
                // Update provider and state
                setState(() {
                  ref
                      .read(recipeMetadataProvider.notifier)
                      .updatePrepTime(prepTime);
                  // Update total time automatically
                  _updateTotalTimeIfPossible();
                });
              },
            )
          else
            TimePickerField(
              label: "Prep time",
              controller: _prepTimeController,
              onTimeSelected: (h, m) {
                // Update prep time variables
                prepHours = h;
                prepMinutes = m;
                // Format and update prep time
                String prepTime = _formatTime(h, m);
                // Update provider and state
                setState(() {
                  ref
                      .read(recipeMetadataProvider.notifier)
                      .updatePrepTime(prepTime);
                  // Update total time automatically
                  _updateTotalTimeIfPossible();
                });
              },
            ),
          const SizedBox(height: 16),
          if (DeviceUtils().isTabletOrIpad(context))
            TimePickerFieldBottomSheet(
              label: "Cook time",
              controller: _cookTimeController,
              onTimeSelected: (h, m) {
                // Update cook time variables
                cookHours = h;
                cookMinutes = m;

                // Format and update cook time
                String cookTime = _formatTime(h, m);

                // Update provider and state
                setState(() {
                  ref
                      .read(recipeMetadataProvider.notifier)
                      .updateCookTime(cookTime);
                  // Update total time automatically
                  _updateTotalTimeIfPossible();
                });
              },
            )
          else
            TimePickerField(
              label: "Cook time",
              controller: _cookTimeController,
              onTimeSelected: (h, m) {
                // Update cook time variables
                cookHours = h;
                cookMinutes = m;

                // Format and update cook time
                String cookTime = _formatTime(h, m);

                // Update provider and state
                setState(() {
                  ref
                      .read(recipeMetadataProvider.notifier)
                      .updateCookTime(cookTime);
                  // Update total time automatically
                  _updateTotalTimeIfPossible();
                });
              },
            ),
          SizedBox(height: 16.h),
          _buildTimeField('Total time', _totalTimeController, (val) {
            ref.read(recipeMetadataProvider.notifier).updateTotalTime(val);
          }),
          SizedBox(height: 30.h),
          Visibility(
            visible: widget.callFromUpdate,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 60.0),
              child: CustomButton(
                text: "Save Changes",
                fontSize: 16,
                onPressed: () {
                  // Ensure ingredients are updated before notifying parent
                  widget.onUpdateData?.call();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField(
      String title,
      String label,
      TextEditingController controller,
      Function(String)? onChanged,
      InputFormatType formatType) {
    return Row(
      children: [
        SizedBox(
          width: DeviceUtils().isTabletOrIpad(context) ? 70 : 180.w,
          child: CustomText(
            text: title,
            color: AppColors.primaryLightTextColor,
            size:
                12,
            weight: FontWeight.w400,
            fontFamily: 'Inter',
          ),
        ),
        SizedBox(width: 10.h),
        Expanded(
          child: IngredientTextField(
              onChanged: onChanged,
              hintText: label,
              controller: controller,
              height: 60.h,
              formatType: formatType),
        ),
      ],
    );
  }

  Widget _buildTimeField(String label, TextEditingController controller,
      Function(String)? onChanged) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        SizedBox(
          width: DeviceUtils().isTabletOrIpad(context) ? 70 : 180.w,
          child: CustomText(
            text: label,
            color: AppColors.primaryLightTextColor,
            size:
                12,
            weight: FontWeight.w400,
            fontFamily: 'Inter',
          ),
        ),
        SizedBox(width: 10.w),
        Expanded(
            child: Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 6,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: CustomText(
            text: controller.text,
            color: AppColors.primaryLightTextColor,
            size:
                DeviceUtils().isTabletOrIpad(context) ? 14 : responsiveFont(14),
            weight: FontWeight.w400,
            fontFamily: 'Inter',
          ),
        )),
      ],
    );
  }
}
