import '../../../../../app/imports/packages_imports.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import 'custom_ingredent_textfield.dart';

class CustomWineWidget extends ConsumerStatefulWidget {
    String? wine;

    CustomWineWidget({super.key, this.wine});

  @override
  ConsumerState<CustomWineWidget> createState() => _CustomWineWidgetState();
}

class _CustomWineWidgetState extends ConsumerState<CustomWineWidget> {
   late TextEditingController wineController;

  @override
  void initState() {
    super.initState();
    // Initialize noteController with value from notesProvider or widget.notes
    wineController = TextEditingController(
      text:  widget.wine ?? '',
    );

  }

  @override
  Widget build(BuildContext context) {

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomeTitleText(title: 'Wine'),
          Sized<PERSON>ox(height: 20.h),
          IngredientTextField(
            onChanged: (val){
              ref.read(recipeMetadataProvider.notifier).updateWineDesc(val);
            },
            hintText: "Description",
            controller: wineController,
            maxLines: 7,
            height: 200.h,
          ),

          // CustomButton(text: "Save changes", onPressed: (){} , width: 210.w, fontSize: 18.sp,),
        ],
      ),
    );
  }
}
