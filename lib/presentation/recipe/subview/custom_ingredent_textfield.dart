import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

// Enum to define input format types
enum InputFormatType {
  text,
  number, // For integers
  double, // For decimal numbers
}

class IngredientTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final bool autoFocus;
  final int maxLines;
  final int? maxLength; // NEW: Optional maxLength parameter
  final double height;
  final bool isEnabled; // Default to true, can be overridden if needed
  final InputFormatType formatType; // Replaces keyboardType and formats
  final Function(String)? onChanged;
  final String? Function(String?)? validator;
  final VoidCallback? onTap;
  final bool showIcon; // Controls whether to show the suffix icon

  const IngredientTextField({
    super.key,
    required this.controller,
    required this.hintText,
    this.height = 50,
    this.maxLines = 1,
    this.maxLength, // NEW: Added to constructor
    this.autoFocus = false,
    this.isEnabled = true,
    this.formatType = InputFormatType.text,
    this.onChanged,
    this.validator,
    this.onTap,
    this.showIcon = true,
  });

  // Get appropriate keyboard type based on formatType
  // TextInputType get _keyboardType {
  //   switch (formatType) {
  //     case InputFormatType.number:
  //       return TextInputType.number;
  //     case InputFormatType.double:
  //       return TextInputType.numberWithOptions(decimal: true);
  //     case InputFormatType.text:
  //     default:
  //       return TextInputType.text;
  //   }
  // }

  TextInputType get _keyboardType {
    if (maxLines > 1) return TextInputType.multiline;

    switch (formatType) {
      case InputFormatType.number:
        return TextInputType.number;
      case InputFormatType.double:
        return const TextInputType.numberWithOptions(decimal: true);
      case InputFormatType.text:
      default:
        return TextInputType.text;
    }
  }

  // Get appropriate input formatters based on formatType
  List<TextInputFormatter> get _inputFormatters {
    switch (formatType) {
      case InputFormatType.number:
        return [FilteringTextInputFormatter.digitsOnly];
      case InputFormatType.double:
        return [
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
        ];
      case InputFormatType.text:
      default:
        return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return
        // Container(
        // margin: EdgeInsets.symmetric(vertical: 6.h),
        // decoration: BoxDecoration(
        //   color: Colors.white,
        //   borderRadius: BorderRadius.circular(8),
        //   boxShadow: const [
        //     BoxShadow(
        //       color: Colors.black12,
        //       blurRadius: 4,
        //       offset: Offset(0, 1),
        //     ),
        //   ],
        // ),
        // child:
        Container(
      margin: EdgeInsets.symmetric(vertical: 6.h),
      height: height,
      child: TextFormField(
        maxLines: maxLines,
        maxLength: maxLength,
        // NEW: Pass maxLength to TextFormField
        controller: controller,
        keyboardType: _keyboardType,
        autofocus: autoFocus,
        enabled: isEnabled,
        inputFormatters: _inputFormatters,
        validator: validator,
        style: context.theme.textTheme.labelMedium!.copyWith(
          color: AppColors.primaryGreyColor,
          fontWeight: FontWeight.w400,
          fontSize: 14, //context.theme.textTheme.titleMedium!.fontSize,
        ),
        decoration: InputDecoration(
          filled: context.theme.inputDecorationTheme.filled,
          fillColor: context.theme.inputDecorationTheme.fillColor,
          hintText: hintText,
          hintStyle: context.theme.inputDecorationTheme.hintStyle!.copyWith(
            fontSize: 14, // context.theme.textTheme.displaySmall!.fontSize
          ),
          errorStyle: context.theme.inputDecorationTheme.errorStyle,
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: AppColors.primaryBorderColor,
              width: 2,
            ),
          ),

          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: Colors.grey[300]!,
              width: 2,
            ),
          ),
          contentPadding: EdgeInsets.all(
            12, // Adjust padding for multi-line
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          suffixIcon: showIcon
              ? controller.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear, size: 18),
                      onPressed: () {
                        controller.clear();
                        if (onChanged != null) {
                          onChanged!('');
                        }
                      },
                    )
                  : null
              : null,
          counterText: '', // Optional: Hide counter if needed
        ),
        onChanged: onChanged,
        onTap: onTap,
      ),
      // ),
    );
  }
}
