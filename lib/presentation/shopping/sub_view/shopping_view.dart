import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/providers/shopping/shopping_notifier.dart';
import 'package:mastercookai/core/providers/shopping/page_state_provider.dart';
import 'package:mastercookai/presentation/shopping/sub_view/shopping_card.dart';
import 'package:mastercookai/presentation/shopping/sub_view/update_shopping_dialog.dart';
import '../../../core/data/models/shopping.dart';
import '../../../core/network/app_status.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/utils/screen_sizer.dart';
import '../../../core/widgets/common_paginated_grid_view.dart';
import '../../../core/widgets/no_data_widget.dart';
import '../../cookbook/widgets/create_cookbook_card.dart';
import '../../shimer/recipe_shimmer.dart';

class ShoppingView extends ConsumerStatefulWidget {
  final Size screenSize;
  final bool isHighRes;
  final String? searchQuery;
  final String selectedTab;

  ShoppingView({
    Key? key,
    required this.screenSize,
    required this.isHighRes,
    this.searchQuery,
    required this.selectedTab,
  }) : super(key: key);

  @override
  ConsumerState<ShoppingView> createState() => _ShoppingViewState();
}

class _ShoppingViewState extends ConsumerState<ShoppingView>
    with AutomaticKeepAliveClientMixin {
  late final PageController _pageController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    final currentPage = ref.read(pageStateProvider).shoppingCurrentPage;
    _pageController = PageController(initialPage: currentPage);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_pageController.hasClients) {
        _pageController.jumpToPage(currentPage);
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final shoppingState = ref.watch(shoppingNotifierProvider);
    final screenSize = widget.screenSize;
    final isHighRes = widget.isHighRes;
    final pageState = ref.watch(pageStateProvider);
    final currentPage = pageState.shoppingCurrentPage;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_pageController.hasClients &&
          _pageController.page?.round() != currentPage) {
        _pageController.jumpToPage(currentPage);
      }
    });



    if (shoppingState.status == AppStatus.loading &&
        (shoppingState.data?.shoppingLists?.isEmpty ?? true)||shoppingState.status == AppStatus.error) {
      final crossAxisCount = DeviceUtils().isTabletOrIpad(context)
          ? 3
          : isHighRes
          ? 5
          : screenSize.width > 600
          ? 5
          : 2;

      return RecipeShimmer(
        crossAxisCount: crossAxisCount,
        itemsPerPage: 8,
        childAspectRatio: 1.0,
      );
    }

     else if (shoppingState.data?.shoppingLists?.isNotEmpty ?? false) {
      final crossAxisCount = DeviceUtils().isTabletOrIpad(context)
          ? 3
          : isHighRes
              ? 5
              : screenSize.width > 600
                  ? 5
                  : 2;
      final itemsPerPage = crossAxisCount * 2;
      final totalItems = (shoppingState.data?.shoppingLists?.length ?? 0) + 1;
      final totalPages = (totalItems / itemsPerPage).ceil();

      return NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollEndNotification) {
            final metrics = scrollNotification.metrics;
            if (metrics.pixels >= metrics.maxScrollExtent - 200 &&
                shoppingState.hasMore &&
                shoppingState.status != AppStatus.loadingMore) {
              ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists(
                    loadMore: true,
                    context: context,
                    search: widget.searchQuery,
                  );
            }
          }
          return false;
        },
        child: Stack(
          children: [
            Column(
              children: [
                Expanded(
                  child: CommonPaginatedGridView(
                    pageController: _pageController,
                    state: shoppingState,
                    totalPages: totalPages,
                    totalItems: totalItems,
                    itemsPerPage: itemsPerPage,
                    crossAxisCount: crossAxisCount,
                    currentSearchQuery: widget.searchQuery ?? '',
                    isHighRes: isHighRes,
                    ref: ref,
                    context: context,
                    onPageChanged: (int page) {
                      ref
                          .read(pageStateProvider.notifier)
                          .updateShoppingPage(page);
                      if (page == totalPages - 1 && shoppingState.hasMore) {
                        ref
                            .read(shoppingNotifierProvider.notifier)
                            .fetchShoppingLists(
                              loadMore: true,
                              context: context,
                              search: widget.searchQuery,
                            );
                      }
                    },
                    itemBuilder:
                        (BuildContext context, int index, int itemIndex) {
                      // Show loader at the last index if data is present and loading more
                      if (itemIndex == totalItems - 1 &&
                          shoppingState.hasMore &&
                          shoppingState.status == AppStatus.loadingMore) {
                        return Center(
                          child: LoadingAnimationWidget.fallingDot(
                            color: Colors.white,
                            size: 50.0,
                          ),
                        );
                      }

                      if (itemIndex == 0) {
                        return ImportCreateCard(
                          importText: "Import Shopping List",
                          title: "Create Shopping List",
                          isRecipe: false,
                          onImport: (){

                          },
                          onCreate: () => ref
                              .read(pageStateProvider.notifier)
                              .showShoppinDialog(context, ref),
                        );
                      }

                      final shoppingIndex = itemIndex - 1;
                      if (shoppingIndex <
                          (shoppingState.data?.shoppingLists?.length ?? 0)) {
                        final shoppingList =
                            shoppingState.data!.shoppingLists![shoppingIndex];

                        final shopping = Shopping(
                          id: shoppingList.id!.toInt(),
                          title: shoppingList.name ?? '',
                          imageUrl: shoppingList.coverImageUrl ?? '',
                          recipeCount:
                              shoppingList.shoppingItemsCount.toString(),
                          createdDate: shoppingList.dateCreated.toString(),
                        );
                        return ShoppingCard(
                            shopping: shopping,
                            isHighRes: isHighRes,
                            shoppingIndex:shoppingIndex,
                            onTap: () {
                              ref
                                  .read(shoppingNotifierProvider.notifier)
                                  .resetToIdle();
                              showUpdateShoppingDialog(
                                context,
                                ref,
                                shoppingId: shoppingList.id.toString(),
                                shoppingName: shoppingList.name,
                              );
                            },
                            selectedTab: widget.selectedTab);
                      }

                      return const SizedBox.shrink();
                    },
                  ),
                ),
              ],
            ),
            if (totalPages > 1)
              Positioned(
                left: 0,
                right: 0,
                bottom: screenSize.height * 0.15,
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: List<Widget>.generate(
                      shoppingState.hasMore ? totalPages + 1 : totalPages,
                      (int index) {
                        if (index >= totalPages) {
                          return const SizedBox.shrink();
                        }
                        return Container(
                          width: screenSize.width * 0.008,
                          height: screenSize.width * 0.008,
                          margin: EdgeInsets.symmetric(
                            horizontal: screenSize.width * 0.004,
                          ),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: currentPage == index
                                ? context.theme.scaffoldBackgroundColor
                                : context.theme.scaffoldBackgroundColor
                                    .withValues(alpha: .2),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    } else if (shoppingState.status != AppStatus.idle &&
        (shoppingState.data?.shoppingLists?.isEmpty ?? true)) {
      if (widget.searchQuery != null && widget.searchQuery!.isNotEmpty) {
        return const NoDataWidget(
          title: "No Shopping Lists Found",
          subtitle:
              "Try adjusting your search terms or create a new shopping list",
          width: 250,
          height: 250,
        );
      } else {
        final crossAxisCount = DeviceUtils().isTabletOrIpad(context)
            ? 3
            : isHighRes
                ? 5
                : screenSize.width > 600
                    ? 5
                    : 2;
        return LayoutBuilder(
          builder: (context, constraints) {
            final size = MediaQuery.of(context).size;
            final aspectRatio =
                ScreenSizer().calculateShoppingSize(size.width, size.height);
            return GridView.builder(
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                mainAxisSpacing: screenSize.height * 0.01,
                crossAxisSpacing: screenSize.width * 0.01,
                childAspectRatio: aspectRatio,
              ),
              itemCount: 1,
              itemBuilder: (context, index) {
                return ImportCreateCard(
                  importText: "Import Shopping List",
                  title: "Create Shopping List",
                  isRecipe: false,
                  onImport: () {

                  },
                  onCreate: () => ref
                      .read(pageStateProvider.notifier)
                      .showShoppinDialog(context, ref),
                );
              },
            );
          },
        );
      }
    } else {
      return const SizedBox.shrink();
    }
  }


}
