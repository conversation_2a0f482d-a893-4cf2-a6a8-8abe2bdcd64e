// lib/screens/shopping_list_detail/widgets/shopping_table.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/core/data/models/shopping.dart';
import 'package:mastercookai/core/providers/shopping/shopping_item_notifier.dart';
import 'package:mastercookai/core/providers/shopping/shopping_notifier.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/table_utils.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/text_fields.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../core/network/app_status.dart';
 import '../../../core/utils/screen_sizer.dart';
import '../../../core/widgets/no_data_widget.dart';
import 'autocomplete_fields.dart';
import 'footer.dart';

Widget buildShoppingTable({
  required BuildContext context,
  required WidgetRef ref,
  required int selectedIndex,
  required List<Map<String, TextEditingController>> newShoppingRows,
  required Map<int, Map<String, TextEditingController>> editableShoppingItems,
  required Set<int> selectedShoppingItemIds,
  required bool isAllShoppingItemsSelected,
  required Function(int, bool?) onCheckboxChanged,
  required Function(ShoppingItem) onMakeEditable,
  required VoidCallback? onAddEmptyRow,
  String? currentSearchQuery,
  required String selectedTab,
}) {
  final shoppingState = ref.watch(shoppingNotifierProvider);
  final shoppingItemState = ref.watch(shoppingItemNotifierProvider);
  final screenSize = MediaQuery.of(context).size;
  final scrollController = ScrollController();

  final selectedShoppingListId =
      shoppingState.data!.shoppingLists!.isNotEmpty &&
              selectedIndex < shoppingState.data!.shoppingLists!.length
          ? shoppingState.data!.shoppingLists![selectedIndex].id!.toInt()
          : null;

  final items = selectedShoppingListId != null
      ? (shoppingItemState.data?.shoppingItems ?? [])
      : [];

  void _loadMoreItems() {
    if (scrollController.position.pixels ==
            scrollController.position.maxScrollExtent &&
        shoppingItemState.status != AppStatus.loading) {
      final notifier = ref.read(shoppingItemNotifierProvider.notifier);
      if (notifier.canFetchMore) {
        notifier.fetchShoppingListsItems(
          id: selectedShoppingListId!,
          search: currentSearchQuery,
        );
      }
    }
  }

  scrollController.addListener(_loadMoreItems);

  final isSearching =
      currentSearchQuery != null && currentSearchQuery.trim().isNotEmpty;
  final hasNoResults = items.isEmpty && newShoppingRows.isEmpty;

  if (isSearching && hasNoResults) {
    return const NoDataWidget(
      title: "Nothing Here Yet",
      subtitle: "No items found. Try searching again or add something new.",
      width: 250,
      height: 250,
    );
  }

  if (!isSearching &&
      hasNoResults &&
      shoppingState.data!.shoppingLists!.isNotEmpty) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      onAddEmptyRow?.call();
    });
  }

  return SizedBox(
    height: ScreenSizer().getVisibleScreenHeight(context) - 150,
    child: Column(
      children: [
        Table(
          columnWidths: const {
            0: FlexColumnWidth(0.7),
            1: FixedColumnWidth(50),
            2: FlexColumnWidth(3.2),
            3: FlexColumnWidth(1.5),
            4: FlexColumnWidth(2),
            5: FlexColumnWidth(2.5),
            6: FlexColumnWidth(2.5),
            7: FlexColumnWidth(1.5),
          },
          border: TableBorder.symmetric(
            inside: BorderSide(color: Colors.grey.shade300),
          ),
          children: [
            buildShoppingHeaderRow(
              context,
              isAllSelected: isAllShoppingItemsSelected,
              onSelectAllChanged: (value) => onCheckboxChanged(-1, value),
            ),
          ],
        ),
        Expanded(
          child: SingleChildScrollView(
            controller: scrollController,
            padding: EdgeInsets.only(bottom: 30.h),
            child: Table(
              columnWidths: const {
                0: FlexColumnWidth(0.7),
                1: FixedColumnWidth(50),
                2: FlexColumnWidth(3.2),
                3: FlexColumnWidth(1.5),
                4: FlexColumnWidth(2),
                5: FlexColumnWidth(2.5),
                6: FlexColumnWidth(2.5),
                7: FlexColumnWidth(1.5),
              },
              border: TableBorder.symmetric(
                inside: BorderSide(color: Colors.grey.shade300),
              ),
              children: [
                ...List.generate(items.length, (index) {
                  final item = items[index];
                  return buildShoppingDataRow(
                    count: index + 1,
                    item: item,
                    isEditable: editableShoppingItems.containsKey(item.id),
                    isSelected: selectedShoppingItemIds.contains(item.id),
                    onCheckboxChanged: (value) =>
                        onCheckboxChanged(item.id, value),
                    onTap: () => onMakeEditable(item),
                    controllers: editableShoppingItems[item.id],
                    context: context,
                    ref: ref,
                  );
                }),
                ...List.generate(newShoppingRows.length, (index) {
                  return buildEditableShoppingRow(
                    count: items.length + index + 1,
                    controllers: newShoppingRows[index],
                    context: context,
                    ref: ref,
                  );
                }),
              ],
            ),
          ),
        ),
        if (shoppingItemState.status == AppStatus.loadingMore)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Center(
              child: LoadingAnimationWidget.fallingDot(
                color: Colors.black54,
                size: 50.0,
              ),
            ),
          ),
        buildFooter(
          context: context,
          ref: ref,
          selectedTab: selectedTab,
          onAddMoreRows: () {
            onAddEmptyRow?.call();
          },
        ),
      ],
    ),
  );
}

TableRow buildShoppingHeaderRow(
  BuildContext context, {
  required bool isAllSelected,
  required Function(bool?) onSelectAllChanged,
}) {
  return TableRow(
    decoration: BoxDecoration(
      color: Colors.grey.shade200,
      borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8), topRight: Radius.circular(8)),
    ),
    children: [
      buildTableCell(buildTitle(context, "#")),
      buildTableCell(
        Checkbox(
          value: isAllSelected,
          onChanged: onSelectAllChanged,
          activeColor: Colors.red,
          side: const BorderSide(color: Colors.red, width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
          checkColor: Colors.white,
        ),
      ),
      buildTableCell(buildTitle(context, "Item")),
      buildTableCell(buildTitle(context, "Amount")),
      buildTableCell(buildTitle(context, "Unit")),
      buildTableCell(buildTitle(context, "Store location")),
      buildTableCell(buildTitle(context, "Recipe")),
      buildTableCell(buildTitle(context, "Cost")),
    ],
  );
}

TableRow buildShoppingDataRow({
  required int count,
  required ShoppingItem item,
  required bool isEditable,
  required bool isSelected,
  required Function(bool?) onCheckboxChanged,
  required VoidCallback onTap,
  required Map<String, TextEditingController>? controllers,
  required BuildContext context,
  required WidgetRef ref,
}) {
  return TableRow(
    decoration: BoxDecoration(
      border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      color: isEditable ? Colors.blue.shade50 : null,
    ),
    children: [
      buildTableCell(Center(child: buildRowText(context, "$count"))),
      buildTableCell(
        Checkbox(
          value: isSelected,
          onChanged: onCheckboxChanged,
          activeColor: Colors.red,
          side: const BorderSide(color: Colors.red, width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
          checkColor: Colors.white,
        ),
      ),
      buildTableCell(
        isEditable
            ? buildItemAutocompleteField(context, controllers!['item']!,
                placeholder: "Item name")
            : GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: buildRowText(context, item.item),
                ),
              ),
      ),
      buildTableCell(
        isEditable
            ? buildAmountTextField(context, controllers!['amount']!,
                placeholder: "Amount")
            : GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: buildRowText(context, item.amount?.toString() ?? "0"),
                ),
              ),
      ),
      buildTableCell(
        isEditable
            ? buildUnitAutocompleteField(context, controllers!['unit']!,
                placeholder: "Unit", ref: ref)
            : GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: buildRowText(context, item.unit),
                ),
              ),
      ),
      buildTableCell(
        isEditable
            ? buildEditableTextField(context, controllers!['storeLocation']!,
                placeholder: "Store location")
            : GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: buildRowText(context, item.storeLocation),
                ),
              ),
      ),
      buildTableCell(
        isEditable
            ? buildRecipeTextField(context, controllers!['recipe']!,
                placeholder: "Recipe")
            : GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: buildRowText(context, item.recipe),
                ),
              ),
      ),
      buildTableCell(
        isEditable
            ? buildCostTextField(context, controllers!['cost']!,
                placeholder: "Cost")
            : GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: buildRowText(context, item.cost.toString()),
                ),
              ),
      ),
    ],
  );
}

TableRow buildEditableShoppingRow({
  required int count,
  required Map<String, TextEditingController> controllers,
  required BuildContext context,
  required WidgetRef ref,
}) {
  return TableRow(
    decoration: BoxDecoration(
      border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
    ),
    children: [
      buildTableCell(Center(child: buildRowText(context, "$count"))),
      buildTableCell(
        Checkbox(
          value: false,
          onChanged: (_) {},
          activeColor: Colors.red,
          side: const BorderSide(color: Colors.red, width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
          checkColor: Colors.white,
        ),
      ),
      buildTableCell(buildItemAutocompleteField(context, controllers['item']!,
          placeholder: "Item name")),
      buildTableCell(buildAmountTextField(context, controllers['amount']!,
          placeholder: "Amount")),
      buildTableCell(buildUnitAutocompleteField(context, controllers['unit']!,
          placeholder: "Unit", ref: ref)),
      buildTableCell(buildEditableTextField(
          context, controllers['storeLocation']!,
          placeholder: "Store location")),
      buildTableCell(buildRecipeTextField(context, controllers['recipe']!,
          placeholder: "Recipe")),
      buildTableCell(buildCostTextField(context, controllers['cost']!,
          placeholder: "Cost")),
    ],
  );
}