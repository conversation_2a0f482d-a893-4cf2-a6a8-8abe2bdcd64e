// lib/screens/shopping_list_detail/helpers/shopping_helpers.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/core/data/models/shopping.dart';
import 'package:mastercookai/core/data/request_query/shopping_item_request.dart'
    as shopping_request;
import 'package:mastercookai/core/providers/shopping/shopping_item_notifier.dart';
import 'package:mastercookai/core/providers/shopping/shopping_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';

/// Helper function to create empty shopping rows
List<Map<String, TextEditingController>> createEmptyShoppingRows(int count) {
  List<Map<String, TextEditingController>> newRows = [];

  for (int i = 0; i < count; i++) {
    final controllers = {
      'item': TextEditingController(),
      'amount': TextEditingController(),
      'unit': TextEditingController(),
      'storeLocation': TextEditingController(),
      'recipe': TextEditingController(),
      'cost': TextEditingController(),
    };

    newRows.add(controllers);
  }

  return newRows;
}

/// Helper function to add listeners to shopping row controllers
void addShoppingRowListeners(
  List<Map<String, TextEditingController>> rows,
  VoidCallback onTextChanged,
) {
  for (var row in rows) {
    for (var controller in row.values) {
      controller.addListener(onTextChanged);
    }
  }
}

Future<void> saveShoppingItems({
  required BuildContext context,
  required WidgetRef ref,
  required String selectedTab,
  required int selectedIndex,
  required List<Map<String, TextEditingController>> newShoppingRows,
}) async {
  if (selectedTab != 'Shopping list') {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please select a shopping list to save items',
          isError: true);
    }
    return;
  }

  final shoppingState = ref.read(shoppingNotifierProvider);
  if (shoppingState.data!.shoppingLists!.isEmpty) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'No shopping list available', isError: true);
    }
    return;
  }

  if (newShoppingRows.isEmpty) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'No new items to save', isError: true);
    }
    return;
  }

  final selectedShoppingListId =
      shoppingState.data!.shoppingLists![selectedIndex].id!.toInt();
  List<shopping_request.ShoppingItem> shoppingItems = [];

  for (var row in newShoppingRows) {
    final item = row['item']?.text.trim() ?? '';
    final amountText = row['amount']?.text.trim() ?? '';
    final unit = row['unit']?.text.trim() ?? '';
    final storeLocation = row['storeLocation']?.text.trim() ?? '';
    final recipe = row['recipe']?.text.trim() ?? '';
    final costText = row['cost']?.text.trim() ?? '';

    if (item.isEmpty) continue;

    // Parse amount - validate if user entered a value, default to 1.0 if empty
    double amount;
    if (amountText.isEmpty) {
      amount = 0.0; // Default value for empty fields
    } else {
      amount = double.tryParse(amountText) ?? 0.0;
      if (!Utils().isValidAmount(amount)) {
        _showAmountValidationError(context, item);
        return;
      }
    }

    final cost = double.tryParse(costText) ?? 0.0;

    shoppingItems.add(shopping_request.ShoppingItem(
      item: item,
      amount: amount,
      unit: unit,
      storeLocation: storeLocation,
      recipe: recipe,
      cost: cost,
    ));
  }

  if (shoppingItems.isEmpty) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please fill in at least one item', isError: true);
    }
    return;
  }

  final shoppingItemRequest = shopping_request.ShoppingItemRequest(
    shoppingItems: shoppingItems,
  );

  try {
    final (success, errorMessage) =
        await ref.read(shoppingNotifierProvider.notifier).addShoppingItems(
              shoppingListId: selectedShoppingListId,
              request: shoppingItemRequest,
            );

    if (success) {
      newShoppingRows.clear();

      // Refresh shopping lists to update product counts
      ref
          .read(shoppingNotifierProvider.notifier)
          .fetchShoppingLists(context: context);

      // Refresh shopping items for the current list
      final shoppingState = ref.read(shoppingNotifierProvider);
      if (shoppingState.data!.shoppingLists!.isNotEmpty &&
          selectedIndex < shoppingState.data!.shoppingLists!.length) {
        final selectedShoppingListId =
            shoppingState.data!.shoppingLists![selectedIndex].id!.toInt();
        ref.read(shoppingItemNotifierProvider.notifier).fetchShoppingListsItems(
              id: selectedShoppingListId,
              search: null,
            );
      }

      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Shopping items saved successfully!', isError: false);
      }
    } else {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Failed to save items: ${errorMessage ?? 'Unknown error'}',
            isError: true);
      }
    }
  } catch (e) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Error saving items: $e', isError: true);
    }
  }
}

Future<(bool success, String? message)> deleteSelectedShoppingItems({
  required BuildContext context,
  required WidgetRef ref,
  required String selectedTab,
  required int selectedIndex,
  required Set<int> selectedShoppingItemIds,
  required bool isAllShoppingItemsSelected,
}) async {
  if (selectedTab != 'Shopping list') {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please select shopping list tab to delete items',
          isError: true);
    }
    return (false, 'Wrong tab selected');
  }

  if (selectedShoppingItemIds.isEmpty) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please select items to delete', isError: true);
    }
    return (false, 'No items selected');
  }

  final shoppingItemState = ref.read(shoppingNotifierProvider);
  final allShoppingItems = shoppingItemState.data!.shoppingLists!;
  final isAllSelected = isAllShoppingItemsSelected ||
      (allShoppingItems.isNotEmpty &&
          selectedShoppingItemIds.length == allShoppingItems.length);

  String confirmationMessage;
  if (isAllSelected) {
    confirmationMessage = 'Are you sure you want to delete ALL shopping items?';
  } else if (selectedShoppingItemIds.length == 1) {
    confirmationMessage = 'Are you sure you want to delete this shopping item?';
  } else {
    confirmationMessage =
        'Are you sure you want to delete ${selectedShoppingItemIds.length} shopping items?';
  }

  final bool? confirmed = await Utils().showCommonConfirmDialog(
    context: context,
    title: 'Delete Shopping Items',
    subtitle: confirmationMessage,
    confirmText: 'Delete',
    cancelText: 'Cancel',
  );

  if (confirmed != true) {
    return (false, 'Deletion cancelled by user');
  }

  final shoppingState = ref.read(shoppingNotifierProvider);
  if (shoppingState.data!.shoppingLists!.isEmpty ||
      selectedIndex >= shoppingState.data!.shoppingLists!.length) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'No shopping list selected', isError: true);
    }
    return (false, 'Invalid shopping list index');
  }

  final selectedShoppingListId =
      shoppingState.data!.shoppingLists![selectedIndex].id!.toInt();
  final deleteType = isAllSelected ? "All" : "CUSTOM";

  final deleteRequest = shopping_request.DeleteShoppingItemsRequest(
    type: deleteType,
    shoppingItemIds:
        deleteType == "All" ? null : selectedShoppingItemIds.toList(),
    status: "DELETE",
  );

  try {
    final (success, errorMessage) = await ref
        .read(shoppingItemNotifierProvider.notifier)
        .deleteShoppingItems(
          shoppingListId: selectedShoppingListId,
          request: deleteRequest,
        );

    if (success) {
      // Refresh shopping lists to update product counts
      if (context.mounted) {
        ref
            .read(shoppingNotifierProvider.notifier)
            .fetchShoppingLists(context: context);
      }

      // Refresh shopping items for the current list
      final shoppingState = ref.read(shoppingNotifierProvider);
      if (shoppingState.data!.shoppingLists!.isNotEmpty &&
          selectedIndex < shoppingState.data!.shoppingLists!.length) {
        final selectedShoppingListId =
            shoppingState.data!.shoppingLists![selectedIndex].id!.toInt();
        ref.read(shoppingItemNotifierProvider.notifier).fetchShoppingListsItems(
              id: selectedShoppingListId,
              search: null,
            );
      }

      if (context.mounted) {
        Utils().showFlushbar(
          context,
          message: 'Items deleted successfully!',
          isError: false,
        );
      }

      return (true, null);
    } else {
      if (context.mounted) {
        Utils().showFlushbar(
          context,
          message: 'Failed to delete items: ${errorMessage ?? 'Unknown error'}',
          isError: true,
        );
      }

      return (false, errorMessage ?? 'Unknown error');
    }
  } catch (e) {
    if (context.mounted) {
      Utils().showFlushbar(
        context,
        message: 'Error deleting items: $e',
        isError: true,
      );
    }
    return (false, 'Exception: $e');
  }
}

Future<bool> markSelectedShoppingItemsAsPurchased({
  required BuildContext context,
  required WidgetRef ref,
  required String selectedTab,
  required int selectedIndex,
  required Set<int> selectedShoppingItemIds,
  required bool isAllShoppingItemsSelected,
}) async {
  if (selectedTab != 'Shopping list') {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please select shopping list tab to mark items as purchased',
          isError: true);
    }
    return false;
  }

  if (selectedShoppingItemIds.isEmpty) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please select items to mark as purchased', isError: true);
    }
    return false;
  }

  final shoppingItemState = ref.read(shoppingNotifierProvider);
  final allShoppingItems = shoppingItemState.data!.shoppingLists!;
  final isAllSelected = isAllShoppingItemsSelected ||
      (allShoppingItems.isNotEmpty &&
          selectedShoppingItemIds.length == allShoppingItems.length);

  final shoppingState = ref.read(shoppingNotifierProvider);
  if (shoppingState.data!.shoppingLists!.isEmpty ||
      selectedIndex >= shoppingState.data!.shoppingLists!.length) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'No shopping list selected', isError: true);
    }
    return false;
  }

  final selectedShoppingListId =
      shoppingState.data!.shoppingLists![selectedIndex].id!.toInt();
  String purchaseType = isAllSelected ? "All" : "CUSTOM";

  final purchaseRequest = shopping_request.DeleteShoppingItemsRequest(
    type: purchaseType,
    shoppingItemIds:
        purchaseType == "All" ? null : selectedShoppingItemIds.toList(),
    status: "PURCHASE",
  );

  try {
    final (success, errorMessage) = await ref
        .read(shoppingItemNotifierProvider.notifier)
        .deleteShoppingItems(
          shoppingListId: selectedShoppingListId,
          request: purchaseRequest,
        );

    if (success) {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Items marked as purchased successfully!', isError: false);
      }
      return true;
    } else {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message:
                'Failed to mark items as purchased: ${errorMessage ?? 'Unknown error'}',
            isError: true);
      }
      return false;
    }
  } catch (e) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Error marking items as purchased: $e', isError: true);
    }
    return false;
  }
}

Future<void> updateShoppingItems({
  required BuildContext context,
  required WidgetRef ref,
  required String selectedTab,
  required int selectedIndex,
  required Map<int, Map<String, TextEditingController>> editableShoppingItems,
  required Set<int> modifiedShoppingItemIds,
}) async {
  if (selectedTab != 'Shopping list') {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please select a shopping list to update items',
          isError: true);
    }
    return;
  }

  final shoppingState = ref.read(shoppingNotifierProvider);
  if (shoppingState.data!.shoppingLists!.isEmpty) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'No shopping list available', isError: true);
    }
    return;
  }

  if (modifiedShoppingItemIds.isEmpty) {
    if (context.mounted) {
      Utils()
          .showFlushbar(context, message: 'No items to update', isError: true);
    }
    return;
  }

  final selectedShoppingListId =
      shoppingState.data!.shoppingLists![selectedIndex].id!.toInt();
  List<shopping_request.ShoppingItem> shoppingItems = [];

  final originalShoppingItems = ref.read(shoppingItemNotifierProvider).data!.shoppingItems;

  for (int itemId in modifiedShoppingItemIds) {
    final controllers = editableShoppingItems[itemId];
    if (controllers != null) {
      final itemName = controllers['item']?.text.trim();
      final amountText = controllers['amount']?.text.trim();
      final unit = controllers['unit']?.text.trim();
      final storeLocation = controllers['storeLocation']?.text.trim();
      final recipe = controllers['recipe']?.text.trim();
      final costText = controllers['cost']?.text.trim();

      final originalItem = originalShoppingItems.firstWhere(
        (item) => item.id == itemId,
        orElse: () => throw Exception('Original item not found'),
      );

      // Use original values as defaults, update with new values if provided
      String finalItemName =
          itemName?.isNotEmpty == true ? itemName! : originalItem.item;

      double amount = originalItem.amount;
      if (amountText?.isNotEmpty == true) {
        final parsedAmount =
            double.tryParse(amountText!) ?? originalItem.amount;
        if (!Utils().isValidAmount(parsedAmount)) {
          _showAmountValidationError(context, finalItemName);
          return;
        }
        amount = parsedAmount;
      }

      double cost = originalItem.cost;
      if (costText?.isNotEmpty == true) {
        cost = double.tryParse(costText!) ?? originalItem.cost;
      }

      shoppingItems.add(shopping_request.ShoppingItem(
        id: itemId,
        item: finalItemName,
        amount: amount,
        unit: unit?.isNotEmpty == true ? unit! : originalItem.unit,
        storeLocation: storeLocation?.isNotEmpty == true
            ? storeLocation!
            : originalItem.storeLocation,
        recipe: recipe?.isNotEmpty == true ? recipe! : originalItem.recipe,
        cost: cost,
      ));
    }
  }

  if (shoppingItems.isEmpty) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Please fill in at least one item', isError: true);
    }
    return;
  }

  final shoppingItemRequest = shopping_request.ShoppingItemRequest(
    shoppingItems: shoppingItems,
  );

  try {
    final (success, errorMessage) =
        await ref.read(shoppingNotifierProvider.notifier).addShoppingItems(
              shoppingListId: selectedShoppingListId,
              request: shoppingItemRequest,
            );

    if (success) {
      for (int itemId in modifiedShoppingItemIds) {
        editableShoppingItems[itemId]?.forEach((key, controller) {
          controller.dispose();
        });
      }
      editableShoppingItems
          .removeWhere((key, value) => modifiedShoppingItemIds.contains(key));
      modifiedShoppingItemIds.clear();

      // Refresh shopping lists to update product counts
      ref
          .read(shoppingNotifierProvider.notifier)
          .fetchShoppingLists(context: context);

      final shoppingState = ref.read(shoppingNotifierProvider);
      if (shoppingState.data!.shoppingLists!.isNotEmpty &&
          selectedIndex < shoppingState.data!.shoppingLists!.length) {
        final selectedShoppingListId =
            shoppingState.data!.shoppingLists![selectedIndex].id!.toInt();
        ref.read(shoppingItemNotifierProvider.notifier).fetchShoppingListsItems(
              id: selectedShoppingListId,
              search: null,
            );
      }

      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Shopping items updated successfully!', isError: false);
      }
    } else {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message:
                'Failed to update items: ${errorMessage ?? 'Unknown error'}',
            isError: true);
      }
    }
  } catch (e) {
    if (context.mounted) {
      Utils().showFlushbar(context,
          message: 'Error updating items: $e', isError: true);
    }
  }
}

void _showAmountValidationError(BuildContext context, String itemName) {
  if (context.mounted) {
    Utils().showFlushbar(context,
        message: 'Amount for "$itemName" must be between 0.01 and 1,000,000',
        isError: true);
  }
}
