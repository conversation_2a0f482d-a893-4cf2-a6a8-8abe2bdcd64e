import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/core/data/models/pantry.dart';
import 'package:mastercookai/core/providers/shopping/pantry_item_notifier.dart';
import 'package:mastercookai/core/providers/shopping/pantry_notifier.dart';
import 'package:mastercookai/core/utils/screen_sizer.dart';
import 'package:intl/intl.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../core/network/app_status.dart';
import '../../../core/widgets/no_data_widget.dart';
import 'autocomplete_fields.dart';
import 'footer.dart';
import 'text_fields.dart';
import 'table_utils.dart';

Widget buildPantryTable({
  required BuildContext context,
  required WidgetRef ref,
  required int selectedIndex,
  required List<Map<String, TextEditingController>> newPantryRows,
  required Map<int, Map<String, TextEditingController>> editablePantryItems,
  required Set<int> selectedPantryItemIds,
  required bool isAllPantryItemsSelected,
  required Function(int, bool?) onCheckboxChanged,
  required Function(PantryItem) onMakeEditable,
  required VoidCallback? onAddEmptyRow,
  String? currentSearchQuery,
  required String selectedTab,
}) {
  final pantryItemState = ref.watch(pantryItemNotifierProvider);
  final pantryState = ref.watch(pantryNotifierProvider);
  final scrollController = ScrollController();

  final selectedPantryListId = pantryState.data!.pantries!.isNotEmpty &&
          selectedIndex < pantryState.data!.pantries!.length
      ? pantryState.data!.pantries![selectedIndex].id
      : null;

  final items =
      selectedPantryListId != null ? (pantryItemState.data?.pantryItems ?? []) : [];

  void _loadMoreItems() {
    if (scrollController.position.pixels ==
            scrollController.position.maxScrollExtent &&
        pantryItemState.status != AppStatus.loading) {
      final notifier = ref.read(pantryItemNotifierProvider.notifier);
      if (notifier.canFetchMore) {
        notifier.fetchPantryItems(
          id: selectedPantryListId!,
          search: currentSearchQuery,
        );
      }
    }
  }

  scrollController.addListener(_loadMoreItems);

  final isSearching =
      currentSearchQuery != null && currentSearchQuery.trim().isNotEmpty;
  final hasNoResults = items.isEmpty && newPantryRows.isEmpty;

  if (isSearching && hasNoResults) {
    return const NoDataWidget(
      title: "Nothing Here Yet",
      subtitle: "No items found. Try searching again or add something new.",
      width: 250,
      height: 250,
    );
  }

  if (!isSearching &&
      hasNoResults &&
      pantryState.data != null &&
      pantryState.data!.pantries!.isNotEmpty) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      onAddEmptyRow?.call();
    });
  }

  return SizedBox(
    height: ScreenSizer().getVisibleScreenHeight(context) - 150,
    child: Column(
      children: [
        Table(
          columnWidths: const {
            0: FlexColumnWidth(0.5),
            1: FixedColumnWidth(50),
            2: FlexColumnWidth(3),
            3: FlexColumnWidth(1.4),
            4: FlexColumnWidth(1.4),
            5: FlexColumnWidth(1),
            6: FlexColumnWidth(1.5),
          },
          border: TableBorder.symmetric(
            inside: BorderSide(color: Colors.grey.shade300),
          ),
          children: [
            buildPantryHeaderRow(
              context: context,
              isAllSelected: isAllPantryItemsSelected,
              onSelectAllChanged: (value) => onCheckboxChanged(-1, value),
            ),
          ],
        ),
        Expanded(
          child: SingleChildScrollView(
            controller: scrollController,
            padding: EdgeInsets.only(bottom: 30.h),
            child: Table(
              columnWidths: const {
                0: FlexColumnWidth(0.5),
                1: FixedColumnWidth(50),
                2: FlexColumnWidth(3),
                3: FlexColumnWidth(1.4),
                4: FlexColumnWidth(1.4),
                5: FlexColumnWidth(1),
                6: FlexColumnWidth(1.5),
              },
              border: TableBorder.symmetric(
                inside: BorderSide(color: Colors.grey.shade300),
              ),
              children: [
                ...List.generate(items.length, (index) {
                  final item = items[index];
                  return buildPantryDataRow(
                    count: index + 1,
                    item: item,
                    isEditable: editablePantryItems.containsKey(item.id),
                    isSelected: selectedPantryItemIds.contains(item.id),
                    onCheckboxChanged: (value) =>
                        onCheckboxChanged(item.id, value),
                    onTap: () => onMakeEditable(item),
                    controllers: editablePantryItems[item.id],
                    context: context,
                    ref: ref,
                  );
                }),
                ...List.generate(newPantryRows.length, (index) {
                  return buildEditablePantryRow(
                    count: items.length + index + 1,
                    controllers: newPantryRows[index],
                    context: context,
                    ref: ref,
                  );
                }),
              ],
            ),
          ),
        ),
        if (pantryItemState.status == AppStatus.loadingMore)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Center(
              child: LoadingAnimationWidget.fallingDot(
                color: Colors.black54,
                size: 50.0,
              ),
            ),
          ),
        buildFooter(
          context: context,
          ref: ref,
          selectedTab: selectedTab,
          onAddMoreRows: () {
            onAddEmptyRow?.call();
          },
        ),
      ],
    ),
  );
}

TableRow buildPantryHeaderRow({
  required BuildContext context,
  required bool isAllSelected,
  required Function(bool?) onSelectAllChanged,
}) {
  return TableRow(
    decoration: BoxDecoration(
      color: Colors.grey.shade200,
      borderRadius: BorderRadius.only(topLeft: Radius.circular(8), topRight: Radius.circular(8)),
    ),
    children: [
      buildTableCell(buildTitle(context, "#")),
      buildTableCell(
        Checkbox(
          value: isAllSelected,
          onChanged: onSelectAllChanged,
          activeColor: Colors.red,
          side: const BorderSide(color: Colors.red, width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
          checkColor: Colors.white,
        ),
      ),
      buildTableCell(buildTitle(context, "Item")),
      buildTableCell(buildTitle(context, "Purchase Date")),
      buildTableCell(buildTitle(context, "Use By Date")),
      buildTableCell(buildTitle(context, "Amount")),
      buildTableCell(buildTitle(context, "Unit")),
    ],
  );
}

TableRow buildPantryDataRow({
  required int count,
  required PantryItem item,
  required bool isEditable,
  required bool isSelected,
  required Function(bool?) onCheckboxChanged,
  required VoidCallback onTap,
  required Map<String, TextEditingController>? controllers,
  required BuildContext context,
  required WidgetRef ref,
}) {
  return TableRow(
    decoration: BoxDecoration(
      border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      color: isEditable ? Colors.blue.shade50 : null,
    ),
    children: [
      buildTableCell(Center(child: buildRowText(context, "$count"))),
      buildTableCell(
        Checkbox(
          value: isSelected,
          onChanged: onCheckboxChanged,
          activeColor: Colors.red,
          side: const BorderSide(color: Colors.red, width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
          checkColor: Colors.white,
        ),
      ),
      buildTableCell(
        isEditable
            ? buildItemAutocompleteField(context, controllers!['item']!, placeholder: "Item name")
            : GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(8.w),
            child: buildRowText(context, item.item),
          ),
        ),
      ),
      buildTableCell(
        isEditable
            ? buildDatePickerField(context, controllers!['purchaseDate'] ?? TextEditingController(), placeholder: "MM/DD/YYYY")
            : GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(8.w),
            child: buildRowText(context, DateFormat('MM/dd/yyyy').format(item.purchasedDate)),
          ),
        ),
      ),
      buildTableCell(
        isEditable
            ? buildDatePickerField(context, controllers!['useByDate']!, placeholder: "MM/DD/YYYY")
            : GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(8.w),
            child: buildRowText(context, DateFormat('MM/dd/yyyy').format(item.useByDate)),
          ),
        ),
      ),
      buildTableCell(
        isEditable
            ? buildAmountTextField(context, controllers!['amount']!, placeholder: "Amount")
            : GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(8.w),
            child: buildRowText(context, item.amount.toString()),
          ),
        ),
      ),
      buildTableCell(
        isEditable
            ? buildUnitAutocompleteField(context, controllers!['unit']!, placeholder: "Unit", ref: ref)
            : GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(8.w),
            child: buildRowText(context, item.unit),
          ),
        ),
      ),
    ],
  );
}

TableRow buildEditablePantryRow({
  required int count,
  required Map<String, TextEditingController> controllers,
  required BuildContext context,
  required WidgetRef ref,
}) {
  return TableRow(
    decoration: BoxDecoration(
      border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
    ),
    children: [
      buildTableCell(Center(child: buildRowText(context, "$count"))),
      buildTableCell(
        Checkbox(
          value: false,
          onChanged: (_) {},
          activeColor: Colors.red,
          side: const BorderSide(color: Colors.red, width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
          checkColor: Colors.white,
        ),
      ),
      buildTableCell(buildItemAutocompleteField(context, controllers['item']!, placeholder: "Item name")),
      buildTableCell(buildDatePickerField(context, controllers['purchaseDate']!, placeholder: "MM/DD/YYYY")),
      buildTableCell(buildDatePickerField(context, controllers['useByDate']!, placeholder: "MM/DD/YYYY")),
      buildTableCell(buildAmountTextField(context, controllers['amount']!, placeholder: "Amount")),
      buildTableCell(buildUnitAutocompleteField(context, controllers['unit']!, placeholder: "Unit", ref: ref)),
    ],
  );
}