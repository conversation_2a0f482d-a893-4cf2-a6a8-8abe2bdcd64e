import 'dart:async';
import 'package:mastercookai/core/data/models/shopping_response.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/shopping/shopping_notifier.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/presentation/shopping/sub_view/custom_tabview.dart';
import 'package:mastercookai/presentation/shopping/sub_view/pantry_view.dart';
import 'package:mastercookai/presentation/shopping/sub_view/shopping_view.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_searchbar.dart';
import '../../core/providers/shopping/pantry_notifier.dart';

class ShoppingScreen extends ConsumerStatefulWidget {
  const ShoppingScreen({super.key});

  @override
  ConsumerState<ShoppingScreen> createState() => _ShoppingListScreenState();
}

class _ShoppingListScreenState extends ConsumerState<ShoppingScreen> {
  final PageController _tabPageController = PageController();
  final TextEditingController searchController = TextEditingController();
  String selectedTab = 'Shopping list';
  int currentTabIndex = 0;
  Timer? _debounceTimer;
  String _currentSearchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          ref
              .read(shoppingNotifierProvider.notifier)
              .fetchShoppingLists(context: context);
          ref.read(pantryNotifierProvider.notifier).fetchPantryLists();
        }
      });
    });
  }

  @override
  void dispose() {
    _tabPageController.dispose();
    searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _handleTabChange(String tab) {
    int newIndex = tab == 'Shopping list' ? 0 : 1;
    if (_tabPageController.hasClients) {
      _tabPageController.animateToPage(
        newIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
    setState(() {
      selectedTab = tab;
      currentTabIndex = newIndex;
    });

    // Trigger search for the new tab if there's a search query
    if (_currentSearchQuery.isNotEmpty) {
      _performSearch(_currentSearchQuery);
    }
  }

  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query != _currentSearchQuery) {
        setState(() {
          _currentSearchQuery = query;
        });
        _performSearch(query);
      }
    });
  }

  void _performSearch(String query) {
    if (selectedTab == 'Shopping list') {
      ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists(
            context: context,
            search: query.isEmpty ? null : query,
          );
    } else {
      ref.read(pantryNotifierProvider.notifier).fetchPantryLists(
            search: query.isEmpty ? null : query,
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isHighRes = screenSize.width > 2000;

    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          Image.asset(
            AssetsManager.background_img,
            fit: BoxFit.cover,
          ),
          Column(
            children: [
              SizedBox(height: 30.h),
              CustomSearchBar(
                width: DeviceUtils().isTabletOrIpad(context) ? 240 : 500.w,
                controller: searchController,
                onChanged: _onSearchChanged,
              ),
              SizedBox(height: 40.h),
              CustomTabView(
                fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 26.sp,
                tabs: ['Shopping list', 'Pantry'],
                selected: selectedTab,
                selectedTabColor: Colors.white,
                bgTabColor: AppColors.lightGreyColor,
                onChanged: _handleTabChange,
                width: DeviceUtils().isTabletOrIpad(context) ? 160 : 300.w,
              ),
              SizedBox(height: 30.h),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: screenSize.width * 0.05,
                  ),
                  child: PageView(
                    controller: _tabPageController,
                    onPageChanged: (index) {
                      setState(() {
                        currentTabIndex = index;
                        selectedTab = index == 0 ? 'Shopping list' : 'Pantry';
                      });
                    },
                    children: [
                      // Shopping List Tab
                      ShoppingView(
                          screenSize: screenSize,
                          isHighRes: isHighRes,
                          searchQuery: _currentSearchQuery.isEmpty
                              ? null
                              : _currentSearchQuery,
                          selectedTab: selectedTab),
                      // Pantry Tab
                      PantryView(
                          screenSize: screenSize,
                          isHighRes: isHighRes,
                          searchQuery: _currentSearchQuery.isEmpty
                              ? null
                              : _currentSearchQuery,
                          selectedTab: selectedTab),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
