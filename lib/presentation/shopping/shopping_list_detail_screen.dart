import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:mastercookai/core/data/models/shopping.dart';
import 'package:mastercookai/core/data/models/pantry.dart';
import 'package:mastercookai/core/providers/shopping/pantry_item_notifier.dart';
import 'package:mastercookai/core/providers/shopping/shopping_notifier.dart';
import 'package:mastercookai/core/providers/shopping/shopping_item_notifier.dart';
import 'package:mastercookai/core/providers/shopping/pantry_notifier.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/action_buttons.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/footer.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/left_side_view.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/pantry_helpers.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/pantry_table.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/shopping_helpers.dart';
import 'package:mastercookai/presentation/shopping/shopping_detail_views/shopping_table.dart';
import '../../app/assets_manager.dart';
import '../../app/imports/packages_imports.dart';
import '../../app/theme/colors.dart';
import '../../core/data/models/pantry_response.dart';
import '../../core/data/models/shopping_response.dart';
import '../../core/utils/screen_sizer.dart';
import '../../core/widgets/custom_appbar.dart';
import '../../core/widgets/custom_drawer.dart';
import '../../core/widgets/custom_searchbar.dart';
import 'sub_view/custom_tabview.dart';
import 'package:mastercookai/core/utils/Utils.dart';

class ShoppingListDetailScreen extends ConsumerStatefulWidget {
  const ShoppingListDetailScreen({super.key});

  @override
  ConsumerState<ShoppingListDetailScreen> createState() =>
      _ShoppingListDetailScreenState();
}

class _ShoppingListDetailScreenState
    extends ConsumerState<ShoppingListDetailScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  String _selectedTab = 'Shopping list';
  final TextEditingController searchController = TextEditingController();
  final TextEditingController topSearchController = TextEditingController();
  int _selectedIndex = 0;
  int _shoppingListIndex = 0; // Track index for Shopping list tab
  int _pantryIndex = 0; // Track index for Pantry tab
  final List<Map<String, TextEditingController>> _newShoppingRows = [];
  final Map<int, Map<String, TextEditingController>> _editableShoppingItems = {};
  final Set<int> _selectedShoppingItemIds = {};
  bool _isAllShoppingItemsSelected = false;
  final Set<int> _modifiedShoppingItemIds = {};
  final List<Map<String, TextEditingController>> _newPantryRows = [];
  final Map<int, Map<String, TextEditingController>> _editablePantryItems = {};
  final Set<int> _selectedPantryItemIds = {};
  bool _isAllPantryItemsSelected = false;
  final Set<int> _modifiedPantryItemIds = {};
  Timer? _itemSearchDebounceTimer;
  String _currentSearchQuery = '';
  String _currentItemSearchQuery = '';
  Timer? _debounceTimer;
  late List<ShoppingLists> shoppingLists;
  late List<Pantries> pantryLists;

  // Loading states for save/update operations
  bool _isSavingShoppingItems = false;
  bool _isUpdatingShoppingItems = false;
  bool _isSavingPantryItems = false;
  bool _isUpdatingPantryItems = false;
  final ScrollController listScrollController = ScrollController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Initialize selected tab from query parameters
      _selectedTab =
          GoRouterState.of(context).uri.queryParameters['tab'] ?? 'Shopping list';

      // Get index from query parameters
      final indexParam = GoRouterState.of(context).uri.queryParameters['index'];
      final int queryIndex = indexParam != null ? int.parse(indexParam) : 0;

      // Set tab-specific index based on initial tab
      if (_selectedTab == 'Shopping list') {
        _shoppingListIndex = queryIndex;
        _pantryIndex = 0; // Reset Pantry index
      } else {
        _pantryIndex = queryIndex;
        _shoppingListIndex = 0; // Reset Shopping list index
      }
      _selectedIndex = _selectedTab == 'Shopping list' ? _shoppingListIndex : _pantryIndex;

      _fetchInitialData();
      _scrollToSelectedIndex();
    });
  }

  void _scrollToSelectedIndex() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (listScrollController.hasClients) {
        const double itemHeight = 120.0;
        final double offset = _selectedIndex * itemHeight;
        final double maxScrollExtent =
            listScrollController.position.maxScrollExtent;
        final double targetOffset = offset.clamp(0.0, maxScrollExtent);

        listScrollController.animateTo(
          targetOffset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  void _fetchInitialData() {
    final shoppingState = ref.read(shoppingNotifierProvider);
    final pantryState = ref.read(pantryNotifierProvider);
    shoppingLists = shoppingState.data?.shoppingLists ?? [];
    pantryLists = pantryState.data?.pantries ?? [];

    if (_selectedTab == 'Shopping list' &&
        shoppingLists.isNotEmpty &&
        _shoppingListIndex < shoppingLists.length) {
      final selectedShoppingListId = shoppingLists[_shoppingListIndex].id!.toInt();
      ref.read(shoppingItemNotifierProvider.notifier).fetchShoppingListsItems(
        id: selectedShoppingListId,
        search: null,
      );
    }

    if (_selectedTab == 'Pantry' &&
        pantryLists.isNotEmpty &&
        _pantryIndex < pantryLists.length) {
      final selectedPantryListId = pantryLists[_pantryIndex].id;
      ref.read(pantryItemNotifierProvider.notifier).fetchPantryItems(
        id: selectedPantryListId ?? 0,
        search: null,
      );
    }
  }

  void _addEmptyShoppingRows(int count) {
    setState(() {
      final newRows = createEmptyShoppingRows(count);
      addShoppingRowListeners(newRows, () {
        setState(() {});
      });
      _newShoppingRows.addAll(newRows);
    });
  }

  void _addEmptyPantryRows(int count) {
    setState(() {
      final newRows = createEmptyPantryRows(count);
      addPantryRowListeners(newRows, () {
        setState(() {});
      });
      _newPantryRows.addAll(newRows);
    });
  }

  void _onItemSearchChanged(String query) {
    _itemSearchDebounceTimer?.cancel();
    _itemSearchDebounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query != _currentItemSearchQuery) {
        setState(() {
          _currentItemSearchQuery = query;
        });
        _performItemSearch(query);
      }
    });
  }

  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query != _currentSearchQuery) {
        setState(() {
          _currentSearchQuery = query;
        });
        _performSearch(query);
      }
    });
  }

  void _performSearch(String query) {
    if (_selectedTab == 'Shopping list') {
      ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists(
        search: query.isEmpty ? null : query,
      );
    } else {
      ref.read(pantryNotifierProvider.notifier).fetchPantryLists(
        search: query.isEmpty ? null : query,
      );
    }
  }

  void _clearSearch() {
    searchController.clear();
    _currentSearchQuery = '';
    if (_selectedTab == 'Shopping list') {
      ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists(search: null);
    } else {
      ref.read(pantryNotifierProvider.notifier).fetchPantryLists(search: null);
    }
  }

  void _performItemSearch(String query) {
    if (_selectedTab == 'Shopping list') {
      if (shoppingLists.isNotEmpty && _shoppingListIndex < shoppingLists.length) {
        final selectedShoppingListId = shoppingLists[_shoppingListIndex].id!.toInt();
        ref.read(shoppingItemNotifierProvider.notifier).fetchShoppingListsItems(
          id: selectedShoppingListId,
          search: query.isEmpty ? null : query,
        );
      }
    } else {
      if (pantryLists.isNotEmpty && _pantryIndex < pantryLists.length) {
        final selectedPantryListId = pantryLists[_pantryIndex].id;
        ref.read(pantryItemNotifierProvider.notifier).fetchPantryItems(
          id: selectedPantryListId ?? 0,
          search: query.isEmpty ? null : query,
        );
      }
    }
  }

  void _onIndexChanged(int index) {
    if (index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
        if (_selectedTab == 'Shopping list') {
          _shoppingListIndex = index;
        } else {
          _pantryIndex = index;
        }

        _selectedShoppingItemIds.clear();
        _isAllShoppingItemsSelected = false;
        _selectedPantryItemIds.clear();
        _isAllPantryItemsSelected = false;
        _newShoppingRows.clear();
        _editableShoppingItems.clear();
        _modifiedShoppingItemIds.clear();
        _newPantryRows.clear();
        _editablePantryItems.clear();
        _modifiedPantryItemIds.clear();
      });

      if (_selectedTab == 'Shopping list') {
        if (shoppingLists.isNotEmpty && _shoppingListIndex < shoppingLists.length) {
          final selectedShoppingListId = shoppingLists[_shoppingListIndex].id!.toInt();
          ref.read(shoppingItemNotifierProvider.notifier).fetchShoppingListsItems(
                id: selectedShoppingListId,
                search: null,
              );
        }
      } else {
        if (pantryLists.isNotEmpty && _pantryIndex < pantryLists.length) {
          final selectedPantryListId = pantryLists[_pantryIndex].id;
          ref.read(pantryItemNotifierProvider.notifier).fetchPantryItems(
                id: selectedPantryListId ?? 0,
                search: null,
              );
        }
      }
    }
    _scrollToSelectedIndex();
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 1100;
    final shoppingState = ref.watch(shoppingNotifierProvider);
    final pantryState = ref.watch(pantryNotifierProvider);
    shoppingLists = shoppingState.data?.shoppingLists ?? [];
    pantryLists = pantryState.data?.pantries ?? [];

    final shoppingItemState = ref.watch(shoppingItemNotifierProvider);
    final pantryItemState = ref.watch(pantryItemNotifierProvider);

    final hasShoppingItems = shoppingItemState.data?.shoppingItems.isNotEmpty ?? false;
    final hasPantryItems = pantryItemState.data?.pantryItems.isNotEmpty ?? false;

    final hasSelectedItems = _selectedTab == 'Shopping list'
        ? hasShoppingItems &&
        (_selectedShoppingItemIds.isNotEmpty || _isAllShoppingItemsSelected)
        : hasPantryItems &&
        (_selectedPantryItemIds.isNotEmpty || _isAllPantryItemsSelected);

    final bool showSaveButton = (_selectedTab == 'Shopping list' &&
            (_newShoppingRows.any((row) => row['item']?.text.trim().isNotEmpty ?? false) ||
                _modifiedShoppingItemIds.isNotEmpty)) ||
        (_selectedTab == 'Pantry' &&
            (_newPantryRows.any((row) => row['item']?.text.trim().isNotEmpty ?? false) ||
                _modifiedPantryItemIds.isNotEmpty));

    final String appBarTitle;
    if (_selectedTab == 'Shopping list') {
      if (shoppingLists.isNotEmpty && _shoppingListIndex < shoppingLists.length) {
        appBarTitle = shoppingLists[_shoppingListIndex].name ?? 'Shopping list';
      } else {
        appBarTitle = 'Shopping list';
      }
    } else {
      if (pantryLists.isNotEmpty && _pantryIndex < pantryLists.length) {
        appBarTitle = pantryLists[_pantryIndex].name ?? 'Pantry';
      } else {
        appBarTitle = 'Pantry';
      }
    }

    return WillPopScope(
        onWillPop: _onBackPressed,
      child: Scaffold(
      key: _scaffoldKey,
      onDrawerChanged: (isOpened) {
        if (!isOpened) {
          if (searchController.text.isNotEmpty) {
            _clearSearch();
          }
        }
      },
      drawer: isSmallScreen
          ? CustomDrawer(
        title: appBarTitle,
        state: _selectedTab == 'Shopping list' ? shoppingState : pantryState,
        buildContent: (dynamic state) {
          return _buildListWidget(
              shoppingState,
              pantryState,
              _selectedTab,
              ref,
              _selectedIndex,
              _currentSearchQuery,
              searchController,
              _onIndexChanged);
        },
      )
          : null,
      appBar: CustomAppBar(
        title: appBarTitle,
        onPressed: () {
          _onBackPressed().then((value) {
            if (value) {
              Navigator.of(context).pop();
            }
          });
        },
        showDrawerIcon: DeviceUtils().isTabletOrIpad(context),
        actions: [
          if (isSmallScreen) ...[
            if (_selectedTab == 'Shopping list' && hasSelectedItems)
              IconButton(
                icon: const Icon(Icons.local_offer, color: Color.fromARGB(255, 147, 147, 147),  size: 24,),
                onPressed: _onMarkPurchased,
                tooltip: 'Mark as Purchased',
              ),
            if (hasSelectedItems)
              IconButton(
                icon: const Icon(Icons.delete_outline, color: Color.fromARGB(255, 147, 147, 147),  size: 24,),
                onPressed: _onDelete,
                tooltip: 'Delete',
              ),
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return ScaleTransition(scale: animation, child: child);
                },
                child: _isSearching
                    ? CustomSearchBar(
                        width: 220,
                        height: 54.h,
                        controller: topSearchController,
                        onChanged: _onItemSearchChanged,
                        onClear: () {
                          setState(() {
                            _isSearching = false;
                          });
                        },
                      )
                    : IconButton(
                        icon: const Icon(
                          Icons.search,
                          size: 24,
                          color: Color.fromARGB(255, 147, 147, 147),
                        ),
                        onPressed: () {
                          setState(() {
                            _isSearching = true;
                          });
                        },
                        tooltip: 'Search items',
                      ),
              ),
            ),
            if (showSaveButton)
              Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: Container(
                    decoration: BoxDecoration(
                      color: (_isSavingShoppingItems ||
                              _isUpdatingShoppingItems ||
                              _isSavingPantryItems ||
                              _isUpdatingPantryItems)
                          ? Colors.red.withOpacity(0.6)
                          : Colors.red,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: TextButton(
                      onPressed: (_isSavingShoppingItems ||
                              _isUpdatingShoppingItems ||
                              _isSavingPantryItems ||
                              _isUpdatingPantryItems)
                          ? null
                          : _onSave,
                      style: TextButton.styleFrom(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      child: Text(
                        _getButtonText(
                          selectedTab: _selectedTab,
                          newShoppingRows: _newShoppingRows.where((row) => row['item']?.text.trim().isNotEmpty ?? false).length,
                          newPantryRows: _newPantryRows.where((row) => row['item']?.text.trim().isNotEmpty ?? false).length,
                          modifiedShoppingItemCount:
                              _modifiedShoppingItemIds.length,
                          modifiedPantryItemCount:
                              _modifiedPantryItemIds.length,
                          isSavingShoppingItems: _isSavingShoppingItems,
                          isUpdatingShoppingItems: _isUpdatingShoppingItems,
                          isSavingPantryItems: _isSavingPantryItems,
                          isUpdatingPantryItems: _isUpdatingPantryItems,
                        ),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14, // Adjust as needed
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  )),
          ] else
            CustomSearchBar(
              width: DeviceUtils().isTabletOrIpad(context) ? 200 : 440.w,
              height: DeviceUtils().isTabletOrIpad(context) ? 30 : 60.h,
              controller: topSearchController,
              onChanged: _onItemSearchChanged,
            ),
        ],
      ),
      body: Row(
        children: [
          if (!isSmallScreen)
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: _buildListWidget(
                    shoppingState,
                    pantryState,
                    _selectedTab,
                    ref,
                    _selectedIndex,
                    _currentSearchQuery,
                    searchController,
                    _onIndexChanged),
              ),
            ),
          Expanded(
            flex: isSmallScreen ? 10 : 6,
            child: Stack(
              fit: StackFit.expand,
              children: [
                AssetsManager.background_img.isNotEmpty
                    ? Image.asset(
                  AssetsManager.background_img,
                  fit: BoxFit.cover,
                )
                    : Container(
                  color: Colors.grey[200],
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      if (!isSmallScreen)
                        buildActionButtonsRow(
                          context: context,
                          selectedTab: _selectedTab,
                          hasSelectedItems: hasSelectedItems,
                          onMarkPurchased: _onMarkPurchased,
                          onDelete: _onDelete,
                          onSave: _onSave,
                          newShoppingRows: _newShoppingRows.where((row) => row['item']?.text.trim().isNotEmpty ?? false).length,
                          newPantryRows: _newPantryRows.where((row) => row['item']?.text.trim().isNotEmpty ?? false).length,
                          modifiedShoppingItemCount:
                              _modifiedShoppingItemIds.length,
                          modifiedPantryItemCount:
                              _modifiedPantryItemIds.length,
                          isSavingShoppingItems: _isSavingShoppingItems,
                          isUpdatingShoppingItems: _isUpdatingShoppingItems,
                          isSavingPantryItems: _isSavingPantryItems,
                          isUpdatingPantryItems: _isUpdatingPantryItems,
                          showSaveButton: showSaveButton,
                        ),
                      if (!isSmallScreen) SizedBox(height: 20.h),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius:
                              BorderRadius.all(Radius.circular(4)),
                              color: Colors.white,
                            ),
                            padding: EdgeInsets.all(4),
                            child: _selectedTab == 'Shopping list'
                                ? buildShoppingTable(
                              context: context,
                              ref: ref,
                              selectedIndex: _selectedIndex,
                              selectedTab: _selectedTab,
                              newShoppingRows: _newShoppingRows,
                              editableShoppingItems:
                              _editableShoppingItems,
                              selectedShoppingItemIds:
                              _selectedShoppingItemIds,
                              isAllShoppingItemsSelected:
                              _isAllShoppingItemsSelected,
                              onCheckboxChanged:
                              _onShoppingCheckboxChanged,
                              onMakeEditable: _makeShoppingItemEditable,
                              onAddEmptyRow: () => _addEmptyShoppingRows(1),
                              currentSearchQuery: _currentItemSearchQuery,
                            ) : buildPantryTable(
                              context: context,
                              ref: ref,
                              selectedIndex: _selectedIndex,
                              newPantryRows: _newPantryRows,
                              selectedTab: _selectedTab,
                              editablePantryItems: _editablePantryItems,
                              selectedPantryItemIds:
                              _selectedPantryItemIds,
                              isAllPantryItemsSelected:
                              _isAllPantryItemsSelected,
                              onCheckboxChanged: _onPantryCheckboxChanged,
                              onMakeEditable: _makePantryItemEditable,
                              onAddEmptyRow: () => _addEmptyPantryRows(1),
                              currentSearchQuery: _currentItemSearchQuery,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    )
    );
  }

  void _onTabChanged(String tab) {
    setState(() {
      _selectedTab = tab;
      if (_selectedTab == 'Shopping list') {
        _selectedIndex = _shoppingListIndex;
      } else {
        _selectedIndex = _pantryIndex;
      }

      _selectedPantryItemIds.clear();
      _isAllPantryItemsSelected = false;
      _selectedShoppingItemIds.clear();
      _isAllShoppingItemsSelected = false;

      if (_selectedTab == 'Shopping list' &&
          shoppingLists.isNotEmpty &&
          _selectedIndex < shoppingLists.length) {
        ref.read(shoppingItemNotifierProvider.notifier).fetchShoppingListsItems(
          id: shoppingLists[_selectedIndex].id!.toInt(),
          search: _currentItemSearchQuery.isEmpty
              ? null
              : _currentItemSearchQuery,
        );
      } else if (_selectedTab == 'Pantry' &&
          pantryLists.isNotEmpty &&
          _selectedIndex < pantryLists.length) {
        ref.read(pantryItemNotifierProvider.notifier).fetchPantryItems(
          id: pantryLists[_selectedIndex].id ?? 0,
          search: _currentItemSearchQuery.isEmpty
              ? null
              : _currentItemSearchQuery,
        );
      }
    });

    if (_currentSearchQuery.isNotEmpty) {
      _performSearch(_currentSearchQuery);
    }

    if (_currentItemSearchQuery.isNotEmpty) {
      _performItemSearch(_currentItemSearchQuery);
    }
    _scrollToSelectedIndex();
  }

  Future<bool> _onBackPressed() async {
    if (_hasMeaningfulUnsavedChanges()) {
      final bool? confirmed = await Utils().showCommonConfirmDialog(
        context: context,
        title: 'Alert',
        subtitle:
            'You have added items but haven’t saved them.\n Do you want to leave without saving?',
        confirmText: 'Yes',
        cancelText: 'No',
      );
      return confirmed ?? false;
    }
    return true;
  }

  void _onShoppingCheckboxChanged(int itemId, bool? value) {
    setState(() {
      if (itemId == -1) {
        final shoppingItemState = ref.read(shoppingItemNotifierProvider);
        final items = shoppingItemState.data?.shoppingItems ?? [];

        if (value == true) {
          _selectedShoppingItemIds.clear();
          for (var item in items) {
            _selectedShoppingItemIds.add(item.id);
          }
          _isAllShoppingItemsSelected = true;
        } else {
          _selectedShoppingItemIds.clear();
          _isAllShoppingItemsSelected = false;
        }
      } else {
        if (value == true) {
          _selectedShoppingItemIds.add(itemId);
        } else {
          _selectedShoppingItemIds.remove(itemId);
        }
        final shoppingItemState = ref.read(shoppingItemNotifierProvider);
        _isAllShoppingItemsSelected = _selectedShoppingItemIds.length ==
            (shoppingItemState.data?.shoppingItems.length ?? 0);
      }
    });
  }

  void _onPantryCheckboxChanged(int itemId, bool? value) {
    setState(() {
      if (itemId == -1) {
        final pantryItemState = ref.read(pantryItemNotifierProvider);
        final items = pantryItemState.data?.pantryItems ?? [];

        if (value == true) {
          _selectedPantryItemIds.clear();
          for (var item in items) {
            _selectedPantryItemIds.add(item.id);
          }
          _isAllPantryItemsSelected = true;
        } else {
          _selectedPantryItemIds.clear();
          _isAllPantryItemsSelected = false;
        }
      } else {
        if (value == true) {
          _selectedPantryItemIds.add(itemId);
        } else {
          _selectedPantryItemIds.remove(itemId);
        }
        final pantryItemState = ref.read(pantryItemNotifierProvider);
        _isAllPantryItemsSelected = _selectedPantryItemIds.length ==
            (pantryItemState.data?.pantryItems.length ?? 0);
      }
    });
  }

  void _onAddMoreRows() {
    if (_selectedTab == 'Shopping list') {
      _addEmptyShoppingRows(1);
    } else {
      _addEmptyPantryRows(1);
    }
  }

  Future<void> _onSave() async {
    if (_isSavingShoppingItems ||
        _isUpdatingShoppingItems ||
        _isSavingPantryItems ||
        _isUpdatingPantryItems) {
      return;
    }

    if (_selectedTab == 'Shopping list') {
      if (_newShoppingRows.any((row) => row['item']?.text.trim().isNotEmpty ?? false)) {
        setState(() {
          _isSavingShoppingItems = true;
        });

        try {
          if (mounted) {
            await saveShoppingItems(
              context: context,
              ref: ref,
              selectedTab: _selectedTab,
              selectedIndex: _selectedIndex,
              newShoppingRows: _newShoppingRows,
            );
          }
        } finally {
          if (mounted) {
            setState(() {
              _isSavingShoppingItems = false;
            });
          }
        }
      }

      if (_modifiedShoppingItemIds.isNotEmpty) {
        setState(() {
          _isUpdatingShoppingItems = true;
        });

        try {
          if (mounted) {
            await updateShoppingItems(
              context: context,
              ref: ref,
              selectedTab: _selectedTab,
              selectedIndex: _selectedIndex,
              editableShoppingItems: _editableShoppingItems,
              modifiedShoppingItemIds: _modifiedShoppingItemIds,
            );
          }
        } finally {
          if (mounted) {
            setState(() {
              _isUpdatingShoppingItems = false;
            });
          }
        }
      }
    } else {
      if (_newPantryRows.any((row) => row['item']?.text.trim().isNotEmpty ?? false)) {
        setState(() {
          _isSavingPantryItems = true;
        });

        try {
          if (mounted) {
            await savePantryItems(
              context: context,
              ref: ref,
              selectedTab: _selectedTab,
              selectedIndex: _selectedIndex,
              newPantryRows: _newPantryRows,
            );
          }
        } finally {
          if (mounted) {
            setState(() {
              _isSavingPantryItems = false;
            });
          }
        }
      }

      if (_modifiedPantryItemIds.isNotEmpty) {
        setState(() {
          _isUpdatingPantryItems = true;
        });

        try {
          if (mounted) {
            await updatePantryItems(
              context: context,
              ref: ref,
              selectedTab: _selectedTab,
              selectedIndex: _selectedIndex,
              editablePantryItems: _editablePantryItems,
              modifiedPantryItemIds: _modifiedPantryItemIds,
            );
          }
        } finally {
          if (mounted) {
            setState(() {
              _isUpdatingPantryItems = false;
            });
          }
        }
      }
    }
  }

  Future<void> _onDelete() async {
    if (_selectedTab == 'Shopping list') {
      final (success, message) = await deleteSelectedShoppingItems(
        context: context,
        ref: ref,
        selectedTab: _selectedTab,
        selectedIndex: _selectedIndex,
        selectedShoppingItemIds: _selectedShoppingItemIds,
        isAllShoppingItemsSelected: _isAllShoppingItemsSelected,
      );

      if (!success) {
        debugPrint("Delete failed: $message");
      } else {
        debugPrint("Delete success");
        setState(() {
          _selectedShoppingItemIds.clear();
          _isAllShoppingItemsSelected = false;
        });
      }
    } else {
      final (success, message) = await deleteSelectedPantryItems(
        context: context,
        ref: ref,
        selectedTab: _selectedTab,
        selectedIndex: _selectedIndex,
        selectedPantryItemIds: _selectedPantryItemIds,
        isAllPantryItemsSelected: _isAllPantryItemsSelected,
      );

      if (!success) {
        debugPrint("Pantry delete failed: $message");
      } else {
        setState(() {
          _selectedPantryItemIds.clear();
          _isAllPantryItemsSelected = false;
        });
      }
    }
  }

  Future<void> _onMarkPurchased() async {
    bool success = await markSelectedShoppingItemsAsPurchased(
      context: context,
      ref: ref,
      selectedTab: _selectedTab,
      selectedIndex: _selectedIndex,
      selectedShoppingItemIds: _selectedShoppingItemIds,
      isAllShoppingItemsSelected: _isAllShoppingItemsSelected,
    );
    if (success) {
      setState(() {
        _selectedShoppingItemIds.clear();
        _isAllShoppingItemsSelected = false;
      });
    }
  }

  void _makeShoppingItemEditable(ShoppingItem item) {
    setState(() {
      if (!_editableShoppingItems.containsKey(item.id)) {
        final itemController = TextEditingController(text: item.item);
        final amountController =
        TextEditingController(text: item.amount.toString());
        final unitController = TextEditingController(text: item.unit);
        final storeLocationController =
        TextEditingController(text: item.storeLocation);
        final recipeController = TextEditingController(text: item.recipe);
        final costController =
        TextEditingController(text: item.cost.toString());

        void addModificationListener() {
          _modifiedShoppingItemIds.add(item.id);
          setState(() {});
        }

        itemController.addListener(addModificationListener);
        amountController.addListener(addModificationListener);
        unitController.addListener(addModificationListener);
        storeLocationController.addListener(addModificationListener);
        recipeController.addListener(addModificationListener);
        costController.addListener(addModificationListener);

        _editableShoppingItems[item.id] = {
          'item': itemController,
          'amount': amountController,
          'unit': unitController,
          'storeLocation': storeLocationController,
          'recipe': recipeController,
          'cost': costController,
        };
      }
    });
  }

  void _makePantryItemEditable(PantryItem item) {
    setState(() {
      if (!_editablePantryItems.containsKey(item.id)) {
        final itemController = TextEditingController(text: item.item);
        final purchaseDateController = TextEditingController(
          text: DateFormat('MM/dd/yyyy').format(item.purchasedDate),
        );
        final useByDateController = TextEditingController(
          text: DateFormat('MM/dd/yyyy').format(item.useByDate),
        );
        final amountController =
        TextEditingController(text: item.amount.toString());
        final unitController = TextEditingController(text: item.unit);

        void addModificationListener() {
          _modifiedPantryItemIds.add(item.id);
          setState(() {});
        }

        itemController.addListener(addModificationListener);
        purchaseDateController.addListener(addModificationListener);
        useByDateController.addListener(addModificationListener);
        amountController.addListener(addModificationListener);
        unitController.addListener(addModificationListener);

        _editablePantryItems[item.id] = {
          'item': itemController,
          'purchaseDate': purchaseDateController,
          'useByDate': useByDateController,
          'amount': amountController,
          'unit': unitController,
        };
      }
    });
  }

  @override
  void dispose() {
    for (var row in _newShoppingRows) {
      row.forEach((key, controller) => controller.dispose());
    }
    for (var row in _newPantryRows) {
      row.forEach((key, controller) => controller.dispose());
    }
    for (var item in _editableShoppingItems.values) {
      item.forEach((key, controller) => controller.dispose());
    }
    for (var item in _editablePantryItems.values) {
      item.forEach((key, controller) => controller.dispose());
    }
    searchController.dispose();
    topSearchController.dispose();
    _itemSearchDebounceTimer?.cancel();
    _debounceTimer?.cancel();
    listScrollController.dispose();
    super.dispose();
  }

  bool _hasMeaningfulUnsavedChanges() {
    bool hasUnsavedNewShoppingItems = _newShoppingRows.any((row) =>
        row['item']?.text.trim().isNotEmpty ?? false);
    bool hasUnsavedNewPantryItems = _newPantryRows.any((row) =>
        row['item']?.text.trim().isNotEmpty ?? false);

    return hasUnsavedNewShoppingItems ||
        hasUnsavedNewPantryItems ||
        _modifiedShoppingItemIds.isNotEmpty ||
        _modifiedPantryItemIds.isNotEmpty;
  }

  Widget _buildListWidget(
      dynamic shoppingState,
      dynamic pantryState,
      String selectedTab,
      WidgetRef ref,
      int selectedIndex,
      String currentSearchQuery,
      TextEditingController searchController,
      Function(int) onIndexChanged,
      ) {
    return Column(
      children: [
        CustomTabView(
          fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 26.sp,
          tabs: ['Shopping list', 'Pantry'],
          selected: _selectedTab,
          width: DeviceUtils().isTabletOrIpad(context) ? 130 : 210.w,
          selectedTabColor: Colors.white,
          bgTabColor: AppColors.lightGreyColor,
          onChanged: _onTabChanged,
        ),
        SizedBox(height: 32.h),
        CustomSearchBar(
          width: DeviceUtils().isTabletOrIpad(context) ? 390 : 600.w,
          controller: searchController,
          onChanged: _onSearchChanged,
        ),
        SizedBox(height: 16.h),
        Expanded(
          child: buildListContent(
            shoppingState,
            pantryState,
            _selectedTab,
            ref,
            _selectedIndex,
            _currentSearchQuery,
            searchController,
                (index) {
              onIndexChanged(index);
              if (MediaQuery.of(context).size.width < 1100) {
                Navigator.of(context).pop();
              }
            },
            listScrollController,
          ),
        ),
      ],
    );
  }
}

String _getButtonText({
  required String selectedTab,
  required int newShoppingRows,
  required int newPantryRows,
  required int modifiedShoppingItemCount,
  required int modifiedPantryItemCount,
  required bool isSavingShoppingItems,
  required bool isUpdatingShoppingItems,
  required bool isSavingPantryItems,
  required bool isUpdatingPantryItems,
}) {
  if (selectedTab == 'Shopping list') {
    if (isSavingShoppingItems) return 'Saving...';
    if (isUpdatingShoppingItems) return 'Updating...';
    if (newShoppingRows > 0) {
      return 'Save (${newShoppingRows + modifiedShoppingItemCount})';
    }
    if (modifiedShoppingItemCount > 0) {
      return 'Update ($modifiedShoppingItemCount)';
    }
  } else {
    if (isSavingPantryItems) return 'Saving...';
    if (isUpdatingPantryItems) return 'Updating...';
    if (newPantryRows > 0) {
      return 'Save (${newPantryRows + modifiedPantryItemCount})';
    }
    if (modifiedPantryItemCount > 0) {
      return 'Update ($modifiedPantryItemCount)';
    }
  }
  return 'Save';
}
