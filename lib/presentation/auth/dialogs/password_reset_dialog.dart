import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import '../../../../core/providers/auth/controllers/password_reset_provider.dart';
import '../../../../core/utils/device_utils.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../core/network/app_status.dart';
import '../../../core/utils/validator.dart';
import '../../../core/widgets/custom_text.dart';

Widget passwordResetDialog(BuildContext context) {
  return Consumer(
    builder: (context, ref, child) {
      final passwordResetState = ref.watch(passwordResetProvider);
      final passwordResetNotifier = ref.read(passwordResetProvider.notifier);
      final emailController = TextEditingController(text: passwordResetNotifier.email);
      final formKey = GlobalKey<FormState>();

      void sendResetLink() {
        if (formKey.currentState!.validate()) {
          passwordResetNotifier.setEmail(emailController.text);
          passwordResetNotifier.sendResetLink(context);
        }
      }

      void closeDialog() {
        passwordResetNotifier.reset();
        Navigator.of(context).pop();
      }

      return Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: EdgeInsets.only(left: 30, top: 10, right: 10, bottom: 20),
          width: 500,
          child: passwordResetState.status == AppStatus.success
              ? _buildSuccessState(context, closeDialog)
              : _buildResetForm(
            context,
            emailController,
            sendResetLink,
            closeDialog,
            formKey,
            passwordResetState.status == AppStatus.loading,
          ),
        ),
      );
    },
  );
}

Widget _buildResetForm(
    BuildContext context,
    TextEditingController emailController,
    VoidCallback sendResetLink,
    VoidCallback closeDialog,
    GlobalKey<FormState> formKey,
    bool isLoading,
    ) {
  return Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      Align(
        alignment: Alignment.topRight,
        child: GestureDetector(
          onTap: closeDialog,
          child: SvgPicture.asset(AssetsManager.ic_close,
          height: 20,
          width: 20,),
        ),
      ),
      Padding(
        padding: EdgeInsets.only(right: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 16),
            CustomText(
              text:  "Reset Your Password",
              color: AppColors.primaryGreyColor,
              size: DeviceUtils().isTabletOrIpad(context) ? 24 : 24,
              weight: FontWeight.w700,
            ),

            SizedBox(height: 8),
            CustomText(
              text: 'Password reset link will be sent on your registered email.',
              color: AppColors.blackTextColor,
              size: DeviceUtils().isTabletOrIpad(context) ? 14 : 14,
              weight: FontWeight.w400,

            ),

            SizedBox(height: 26),
            Form(
              key: formKey,
              child:
              CustomInputField(
                hintText:'Email Id',
                controller: emailController,
                keyboardType: TextInputType.emailAddress,
                   validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter email';
                    }
                    if (!value.contains('@')) {
                      return 'Enter a valid email';
                    }
                    return null;
                  },
              ),

            ),
            SizedBox(height: 25),

            CustomButton(
              text: 'Send link',
              height: 30,
              width: 110,
              fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : responsiveFont(12),
              isLoading: isLoading,
              onPressed: sendResetLink,
            ),
            SizedBox(height: 20),
            TextButton(
              onPressed: () {
                Utils().launchURL(url: 'https://support.mastercook.com/hc/en-us');
              },
              child: Text(
                'Need Help?',
                style: TextStyle(
                  color: AppColors.primaryBorderColor,
                  fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 12,
                  fontWeight: FontWeight.w400,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        ),
      ),
    ],
  );
}

Widget _buildSuccessState(BuildContext context, VoidCallback closeDialog) {
  return Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      Align(
        alignment: Alignment.topRight,
        child: GestureDetector(
          onTap: closeDialog,
          child: SvgPicture.asset(AssetsManager.ic_close),
        ),
      ),
      Padding(
        padding: EdgeInsets.only(right: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(AssetsManager.ic_tick),
            SizedBox(height: 8),
            CustomText(
              text:  'Password reset link \nsent successfully.',
              color: AppColors.blackTextColor,
              size: DeviceUtils().isTabletOrIpad(context) ? 14 : 14,
              weight: FontWeight.w400,
            ),
            SizedBox(height: 25),
            CustomButton(
              text: 'Check Email',
              isUnderline: true,
              height: 30,
              width: 120,
              fontSize: DeviceUtils().isTabletOrIpad(context) ? 15 : responsiveFont(18),
              onPressed: () {
                Utils().launchURL(url: 'mailto:');
                closeDialog();
              },
            ),
          ],
        ),
      ),
    ],
  );
}