import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import '../../../app/assets_manager.dart';
import '../../../core/helpers/local_storage_service.dart';
import '../../../core/providers/auth/auth_controller.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )
      ..addListener(() => setState(() {})) // Force rebuild
      ..forward();

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _checkLogin();
        // context.go('/login');
      }
    });

  }

  Future<void> _checkLogin() async {
    final localStorage = ref.read(localStorageProvider);

    final isLoggedIn = await localStorage.isLoggedIn();

    ref.read(authNotifierProvider.notifier).setLoggedIn(isLoggedIn);

    print("isLoggedIn  $isLoggedIn");
    // Navigate to the correct screen
    if (isLoggedIn) {
      context.go('/home');
    } else {
      context.go('/login');
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFBFBFB),
      body: Center(
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(60.r),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Circle logo
              Container(
                width: 420,
                height: 420,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(40.r)),
                  image: DecorationImage(
                    image: AssetImage(AssetsManager.circle_logo),
                    fit: BoxFit.contain,
                  ),
                ),
              ),

              SizedBox(height: 30.h),

              // Animated progress bar
              AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(10.r),
                    child: SizedBox(
                      width: 350,
                      child: Stack(
                        children: [
                          Container(
                            margin: EdgeInsets.all(4.w), // Match SVG padding
                            height: 13.h, // Increased height for visibility
                            width: 350,
                            decoration: BoxDecoration(
                              color: Colors.white, // Contrasting color
                              borderRadius:
                                  BorderRadius.circular(12.r), // Half of height
                            ),
                          ),
                          // Animated Progress
                          Container(
                            margin: EdgeInsets.all(4.w), // Match SVG padding
                            height: 13.h, // Increased height for visibility
                            width: 350.w * _controller.value,
                            decoration: BoxDecoration(
                              color:
                                  AppColors.primaryColor, // Contrasting color
                              borderRadius:
                                  BorderRadius.circular(12.r), // Half of height
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
