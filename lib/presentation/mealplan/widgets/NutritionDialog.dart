  import 'package:flutter/material.dart';
  import 'package:flutter_svg/flutter_svg.dart';
  import 'package:mastercookai/app/assets_manager.dart';
  import 'package:mastercookai/app/imports/core_imports.dart';

  import '../../../../../app/imports/packages_imports.dart';
  import '../../cookbook/widgets/custom_title_text.dart';
  import 'NutritionRow.dart';

  class NutritionDialog extends ConsumerStatefulWidget {

    const NutritionDialog({super.key});

    @override
    ConsumerState<NutritionDialog> createState() => _NutritionDialogState();
  }

  class _NutritionDialogState extends ConsumerState<NutritionDialog> {

    @override
    Widget build(BuildContext context) {
      return Dialog(
        backgroundColor:Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Container(
            color: Colors.white,
            width: 700.w,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(

                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    CustomeTitleText(title: "Nutrition Analysis" ),

                    Spacer(),

                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: _closeButton(context),
                    ),
                  ],
                ),
                Container(
                  margin: EdgeInsets.only(top: 10.h),
                  child: SvgPicture.asset(AssetsManager.label , width: 700.w, height: 800.h,)
                )
              ],
            ),
          ),
        ),
      );
    }


  }

  Widget _closeButton(BuildContext context) {
    return Align(
      alignment: Alignment.topRight,
      child: IconButton(
        icon: const Icon(IconsaxPlusBold.close_circle, color: Colors.red),
        onPressed: () => Navigator.of(context).pop(),
      ),
    );
  }

  class NutritionFactsDialog extends StatelessWidget {
    @override
    Widget build(BuildContext context) {
      return Dialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      elevation: 10,
      backgroundColor: Colors.white,
      child: ClipRRect(
      borderRadius: BorderRadius.circular(20),
      // important to clip children also
        child: Container(
          width: 700.w,
          padding: EdgeInsets.only(
            left: 50,   // Start (left)
            right: 10,  // End (right)
            bottom: 10,  // Bottom
          ),
          color: Colors.white,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Align(
                  alignment: Alignment.topRight,
                  child: GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: _closeButton(context),
                  ),
                ),
                Center(
                  child: Text( "Nutrition Analysis" ,  style: context.theme.textTheme.labelLarge!.copyWith(
                    fontSize: 35.sp,
                    color: AppColors.primaryGreyColor,
                    fontWeight: FontWeight.w700,
                  ) ),
                ),

                Container(
                  margin:  EdgeInsets.only(
                    right: 50,  // End (right)
                    top: 30,
                    bottom: 20// Top
                  ),
                  decoration: BoxDecoration(
                    //color: AppColors.primaryColor,
                    borderRadius: BorderRadius.circular(2),
                    border: Border(
                      left: BorderSide(color: Colors.black,width: 4),
                      top: BorderSide(color: Colors.black, width: 4),
                      bottom: BorderSide(color: Colors.black, width: 4),
                      right: BorderSide(color: Colors.black, width: 4),
                      // No right border
                    ),
                  ),
                  child: Padding(
                    padding:  EdgeInsets.all(8.0),
                    child: Column(
                      children: [
                        Align(
                          alignment: Alignment.topLeft,
                          child: Text(
                              'Nutrition Facts',
                              style: context.theme.textTheme.labelLarge!.copyWith(
                                fontSize: 35.sp,
                                color: AppColors.blackColor,
                                fontWeight: FontWeight.w700,
                              )
                          ),
                        ),
                        SizedBox(height: 36.h),
                        Divider(thickness: 1, color: Colors.black),
                        Container(
                          color: AppColors.yellowRowColor,
                          child: Column(
                            children: [
                              Align(
                                alignment: Alignment.topLeft,
                                child: Text(
                                  '2 Servings per container',
                                    style: context.theme.textTheme.displaySmall!.copyWith(
                                      fontSize: 18.sp,
                                      color: AppColors.blackColor,
                                      fontWeight: FontWeight.w400,
                                    )
                                ),
                              ),
                              SizedBox(height: 10),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                      'Serving size',
                                      style: context.theme.textTheme.displaySmall!.copyWith(
                                        fontSize: 18.sp,
                                        color: AppColors.blackColor,
                                        fontWeight: FontWeight.w700,
                                      )
                                  ),
                                  Text(
                                      '1 cup (140g)',
                                      style: context.theme.textTheme.displaySmall!.copyWith(
                                        fontSize: 18.sp,
                                        color: AppColors.blackColor,
                                        fontWeight: FontWeight.w700,
                                      )
                                  ),
                                  SizedBox(width: 40),

                                ],
                              ),
                            ],
                          ),
                        ),

                        Divider(thickness: 8, color: Colors.black),
                        SizedBox(height: 5),
                        Container(
                          color: Colors.lightBlueAccent.withOpacity(.2),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Amount per serving',
                                      style: context.theme.textTheme.displaySmall!.copyWith(
                                        fontSize: 14.sp,
                                        color: AppColors.blackColor,
                                        fontWeight: FontWeight.w700,
                                      )
                                  ),
                                  Text(
                                    '160',
                                      style: context.theme.textTheme.labelLarge!.copyWith(
                                        fontSize: 30.sp,
                                        color: AppColors.blackColor,
                                        fontWeight: FontWeight.w700,
                                      )
                                  ),
                                  SizedBox(width: 40),

                                ],
                              ),
                              Align(
                                alignment: Alignment.topLeft,
                                child: Text(
                                    'Calories',
                                    style: context.theme.textTheme.labelLarge!.copyWith(
                                      fontSize: 24.sp,
                                      color: AppColors.blackColor,
                                      fontWeight: FontWeight.w700,
                                    )
                                ),
                              ),
                            ],
                          ),
                        ),

                        Divider(thickness: 4, color: Colors.black),
                        SizedBox(height: 4),
                        Container(
                          color: AppColors.yellowRowColor,
                          child: Column(
                            children: [
                              Align(
                                alignment: Alignment.topCenter,
                                child: Text(
                                    '% Daily Value',
                                    style: context.theme.textTheme.labelLarge!.copyWith(
                                      fontSize: 18.sp,
                                      color: AppColors.blackColor,
                                      fontWeight: FontWeight.w700,
                                    )
                                ),
                              ),
                              Divider(thickness: 1, color: Colors.black),

                              NutritionRow(
                                name: 'Total Fat 8g',
                                percent: '10%',
                                isBold: true,
                              ),
                              Divider(thickness: 1, color: Colors.black),
                              NutritionRow(
                                name: 'Saturated Fat 8g',
                                percent: '10%',
                                indent: true,
                              ),
                              Divider(thickness: 1, color: Colors.black),
                              NutritionRow(
                                name: 'Trans Fat 8g',
                                indent: true,
                              ),
                              Divider(thickness: 1, color: Colors.black),
                              NutritionRow(
                                name: 'Cholesterol 0mg',
                                percent: '0%',
                                isBold: true,
                              ),
                              Divider(thickness: 1, color: Colors.black),
                              NutritionRow(
                                name: 'Sodium 60mg',
                                percent: '3%',
                                isBold: true,
                              ),
                              Divider(thickness: 1, color: Colors.black),
                              NutritionRow(
                                name: 'Total Carbohydrate 21g',
                                percent: '8%',
                                isBold: true,
                              ),
                              Divider(thickness: 1, color: Colors.black),

                              NutritionRow(
                                name: 'Dietary Fiber 3g',
                                percent: '11%',
                                indent: true,
                              ),
                              Divider(thickness: 1, color: Colors.black),
                              NutritionRow(
                                name: 'Total Sugars 15g',
                                indent: true,
                              ),
                              Divider(thickness: 1, color: Colors.black),
                              NutritionRow(
                                name: 'Includes 5g added sugars',
                                percent: '10%',
                                indent: true,
                              ),
                              Divider(thickness: 1, color: Colors.black),
                              NutritionRow(
                                name: 'Protein 3g',
                                percent: '10%',
                                isBold: true,
                              ),

                            ],
                          ),
                        ),

                        Divider(thickness: 5, color: Colors.black),
                        Container(
                          color: Colors.greenAccent.withOpacity(.2),
                          child: Column(
                            children: [
                              NutritionRow(
                                name: 'Vitamin D 5mcg',
                                percent: '25%',
                              ),
                              Divider(thickness: 1, color: Colors.black),
                              NutritionRow(
                                name: 'Calcium 20mg',
                                percent: '2%',
                                isBold: true,
                              ),
                              Divider(thickness: 1, color: Colors.black),
                              NutritionRow(
                                name: 'Iron 1mg',
                                percent: '6%',
                                isBold: true,
                              ),
                              Divider(thickness: 1, color: Colors.black),
                              NutritionRow(
                                name: 'Potassium 230mg',
                                percent: '4%',
                                isBold: true,
                              ),
                            ],
                          ),
                        ),

                        Divider(thickness: 5, color: Colors.black),
                        SizedBox(height: 5),
                        Text(
                          '*The % Daily Value tells you how much a nutrient in a serving food contributes to a daily diet.2000 calories a day is used for general nutrition advice.',
                            style: context.theme.textTheme.labelLarge!.copyWith(
                              fontSize: 12.sp,
                              color: context.theme.hintColor,
                              fontWeight: FontWeight.w300,
                            )
                        ),
                        SizedBox(height: 10),
                      ],
                    ),
                  ),
                )

              ],
            ),
          ),
        ),
      ),
      );
    }


  }

