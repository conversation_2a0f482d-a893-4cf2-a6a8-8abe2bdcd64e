import 'dart:async';

import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_drawer.dart';
import 'package:mastercookai/core/widgets/custom_searchbar.dart';
import 'package:mastercookai/presentation/cookbook/widgets/cookbook_filter.dart';
import 'package:mastercookai/presentation/cookbook/widgets/paginated_gridview.dart';
import 'package:mastercookai/presentation/cookbook/widgets/update_cookbook_dialog.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_appbar.dart';
import '../../core/network/app_status.dart';
import '../../core/providers/categories_notifier.dart';
import '../../core/providers/cookbook_notifier.dart';
import '../../core/providers/recipe/metadata_provider.dart';
import '../../core/providers/recipe_notifier.dart';
import '../../core/providers/cuisines_notifier.dart';
import '../../core/utils/device_utils.dart';
import '../../core/widgets/custom_button.dart';
import '../../core/widgets/no_data_widget.dart';
import '../shimer/recipe_shimmer.dart';
import '../shimer/cookbook_list_shimmer.dart';
import 'widgets/cookbook_details_card.dart';
import 'widgets/drop_down_filter.dart';

class CookbookDetailScreen extends ConsumerStatefulWidget {
  const CookbookDetailScreen({super.key});

  @override
  ConsumerState<CookbookDetailScreen> createState() =>
      _CookbookDetailsScreenState();
}

class _CookbookDetailsScreenState extends ConsumerState<CookbookDetailScreen> {
  bool _isSearching = false;
  TextEditingController cookbookSearchController = TextEditingController();
  TextEditingController recipeSearchController = TextEditingController();
  int totalPages = 0;
  int _selectedCookbookIndex = 0;
  int cuisine = 0;
  int category = 0;
  int cookbookId = 0;
  String cookbookName = '';
  String sortOption = ''; // Default sort option
  final ScrollController _scrollController = ScrollController();
  Timer? _cookbookSearchDebounce;
  Timer? _recipeSearchDebounce;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  final Map<String, List<String>> _filterOptions = {
    "Cuisine": [],
    "Category": [],
    "Sort": ['Name (A-Z)', 'Name (Z-A)', 'Most Recent', 'Oldest First'],// Added new sort options
  };

  Map<String, String> _selectedFilters = {
    "Cuisine": "",
    "Category": "",
    "Sort": "Sort By", // Default selected sort option
  };

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    recipeSearchController.addListener(_onSearchChanged);
    cookbookSearchController.addListener(_onCookbookSearchChanged);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final id = GoRouterState.of(context).uri.queryParameters['id'];
      final name = GoRouterState.of(context).uri.queryParameters['name'];
      if (id == null || name == null) {
        print('Invalid id or name in route parameters: id=$id, name=$name');
        return;
      }

      cookbookId = int.parse(id);

      final cookbookState = ref.read(cookbookNotifierProvider);
      setState(() {
        if (cookbookState.data != null && cookbookState.data!.isNotEmpty) {
          final index =
          cookbookState.data!.indexWhere((cb) => cb.id == cookbookId);
          _selectedCookbookIndex = index != -1 ? index : 0;
          cookbookId = cookbookState.data![_selectedCookbookIndex].id;
          cookbookName = cookbookState.data![_selectedCookbookIndex].name;
          print(
              'Initialized: selected index=$_selectedCookbookIndex, cookbookId=$cookbookId, data length=${cookbookState.data!.length}');
          //scrollToSelectedCookbook(_selectedCookbookIndex);
        } else {
          print('No cookbooks found in state');
        }
      });
      if (cookbookState.data != null && cookbookState.data!.isNotEmpty) {
        fetchRecipe();
      }

      ref
          .read(categoriesNotifierProvider.notifier)
          .fetchCategories(context: context);
      ref
          .read(cuisinesNotifierProvider.notifier)
          .fetchCuisines(context: context);

      final cuisines = ref.read(cuisinesNotifierProvider).data ?? [];
      _filterOptions['Cuisine'] = cuisines.map((c) => c.name ?? '').toList();

      final categories = ref.read(categoriesNotifierProvider).data ?? [];
      _filterOptions['Category'] = categories.map((c) => c.name ?? '').toList();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    cookbookSearchController.dispose();
    recipeSearchController.dispose();
    _scrollController.dispose();
    _recipeSearchDebounce?.cancel();
    _cookbookSearchDebounce?.cancel();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final cookbookState = ref.read(cookbookNotifierProvider);
      if (cookbookState.hasMore &&
          cookbookState.status != AppStatus.loadingMore) {
        ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
          loadMore: true,
          context: context,

        );
      }
    }
  }

  void _onSearchChanged() {
    final text = recipeSearchController.text.trim();
    if (text.length >= 3) {
      _debounceSearch(text);
    }
  }

  void _onCookbookSearchChanged() {
    final text = cookbookSearchController.text.trim();
    if (text.length >= 3) {
      _debounceCookbookSearch(text);
    }
  }

  void _debounceCookbookSearch(String query) {
    if (_cookbookSearchDebounce?.isActive ?? false) {
      _cookbookSearchDebounce!.cancel();
    }
    _cookbookSearchDebounce =
        Timer(const Duration(milliseconds: 500), () async {
          bool isSuccess =
          await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
            context: context,
            loadMore: false,
            search: query,

          );
          if (isSuccess) {
            final cookbookState = ref.read(cookbookNotifierProvider);
            if (cookbookState.data != null && cookbookState.data!.isNotEmpty) {
              cookbookId = cookbookState.data![0].id;
              _selectedCookbookIndex = 0;
              fetchRecipe();
            } else {
              cookbookId = 0;
            }
          } else {
            cookbookId = 0;
          }
        });
  }

  void _debounceSearch(String query) {
    if (_recipeSearchDebounce?.isActive ?? false) {
      _recipeSearchDebounce!.cancel();
    }
    _recipeSearchDebounce = Timer(const Duration(milliseconds: 500), () {
      fetchRecipe();
    });
  }

  // Check if any filters are applied
  // bool get _isAnyFilterApplied {
  //   return _selectedFilters.values.any((value) => value.isNotEmpty) ||
  //       recipeSearchController.text.trim().isNotEmpty ||
  //       cookbookSearchController.text.trim().isNotEmpty;
  // }

  bool get _isAnyFilterApplied {
    final hasCuisineFilter = _selectedFilters['Cuisine']?.isNotEmpty ?? false;
    final hasCategoryFilter = _selectedFilters['Category']?.isNotEmpty ?? false;
    final hasSortFilter =  (_selectedFilters['Sort'] != null && _selectedFilters['Sort'] != 'Sort By');
    return hasCuisineFilter || hasCategoryFilter || hasSortFilter;
  }

  @override
  Widget build(BuildContext context) {
    final cookbookState = ref.watch(cookbookNotifierProvider);
    final recipeState = ref.watch(recipeNotifierProvider);
    final screenSize = MediaQuery.of(context).size;
    final isHighRes = screenSize.width > 2000;
    final isSmallScreen = screenSize.width <= 1100; // Define small screen breakpoint
    final crossAxisCount = (DeviceUtils().isTabletOrIpad(context) ? 3 : isHighRes ? 4 : 4).toInt();

    return Scaffold(
      key: _scaffoldKey,
      onDrawerChanged: (isOpened) {
        if (!isOpened) {
          if (cookbookSearchController.text.isNotEmpty) {
            onClearFiltersPressed(context);
          }
        }
      },
      appBar: CustomAppBar(
        title: cookbookName,
        showDrawerIcon: DeviceUtils().isTabletOrIpad(context),
        actions: [
          if (!isSmallScreen)
          Wrap(
            spacing: 8,
            children: _filterOptions.keys.map((filterName) {
              return DropdownFilter(
                filterName: filterName,
                selectedFilters: _selectedFilters,
                filterOptions: _filterOptions,
                onFilterChanged: (filterName, selectedValue) {
                  setState(() {
                    _selectedFilters[filterName] = selectedValue;
                    switch (filterName) {
                      case 'Cuisine':
                        final cuisineModel = ref
                            .read(cuisinesNotifierProvider)
                            .data
                            ?.firstWhere((c) => c.name == selectedValue);
                        if (cuisineModel != null) {
                          cuisine = cuisineModel.id!;
                          ref
                              .read(recipeMetadataProvider.notifier)
                              .updateCuisineId(cuisineModel.id!);
                          fetchRecipe();
                        }
                        break;
                      case 'Category':
                        final categoryModel = ref
                            .read(categoriesNotifierProvider)
                            .data
                            ?.firstWhere((c) => c.name == selectedValue);
                        if (categoryModel != null) {
                          category = categoryModel.id!;
                          ref
                              .read(recipeMetadataProvider.notifier)
                              .updateCategoryId(categoryModel.id);
                          fetchRecipe();
                        }
                        break;
                      case 'Sort':
                        sortOption = selectedValue;
                        // ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
                        //   context: context,
                        //   loadMore: false,
                        //
                        // );
                        fetchRecipe();
                        break;
                    }
                  });
                },
              );
            }).toList(),
          ),
          SizedBox(width: 12.w),
          if (isSmallScreen)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return ScaleTransition(scale: animation, child: child);
                },
                child: _isSearching
                    ? CustomSearchBar(
                        width: 200,
                        height: 60.h,
                        controller: recipeSearchController,
                        onClear: () {
                          fetchRecipe(); // Refresh recipes when search is cleared
                          setState(() {
                            _isSearching = false;
                          });
                        },
                      )
                    : IconButton(
                        icon: Icon(
                          Icons.search,
                          size: 24,
                          color: const Color.fromARGB(255, 147, 147, 147),
                        ),
                        onPressed: () {
                          setState(() {
                            _isSearching = true;
                          });
                        },
                        tooltip: 'Search Recipes',
                      ),
              ),
            ),
          if (!isSmallScreen)
          CustomSearchBar(
            width: 440.w,
            height: 60.h,
            controller: recipeSearchController,
            onClear: () {
              fetchRecipe(); // Refresh recipes when search is cleared
            },
          ),
             if (isSmallScreen)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: IconButton(
                  icon: Icon(
                    Icons.filter_list,
                    size: 24,
                    color: const Color.fromARGB(255, 147, 147, 147), // set the color if needed
                  ),
                  onPressed: () async {
                    final selectedFilters = await showModalBottomSheet<Map<String, String>>(
                      context: context,
                      isScrollControlled: false,
                      constraints: BoxConstraints(
                        maxWidth: MediaQuery.of(context).size.width > 800 ? 800 : double.infinity,
                      ),
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                      ),
                      builder: (context) => Padding(
                        padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom,
                        ),
                        child: CookbookFilter(initialFilters: _selectedFilters),
                      ),
                    );

                    if (selectedFilters != null) {
                      setState(() {
                        _selectedFilters = selectedFilters;
                        try {
                          final cuisineModel = ref
                              .read(cuisinesNotifierProvider)
                              .data
                              ?.firstWhere((c) => c.name == _selectedFilters['Cuisine']);
                          if (cuisineModel != null) {
                            cuisine = cuisineModel.id!;
                            ref
                                .read(recipeMetadataProvider.notifier)
                                .updateCuisineId(cuisineModel.id!);
                          }
                        } catch (e) {
                          // Handle case where no cuisine is found
                        }

                        try {
                          final categoryModel = ref
                              .read(categoriesNotifierProvider)
                              .data
                              ?.firstWhere((c) => c.name == _selectedFilters['Category']);
                          if (categoryModel != null) {
                            category = categoryModel.id!;
                            ref
                                .read(recipeMetadataProvider.notifier)
                                .updateCategoryId(categoryModel.id);
                          }
                        } catch (e) {
                          // Handle case where no category is found
                        }
                        sortOption = _selectedFilters['Sort'] ?? '';
                      });
                      fetchRecipe();
                    }
                  },
                tooltip: 'Cookbooks',
              ),
            ),
             if (_isAnyFilterApplied)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: CustomButton(
                text: "Clear Filter",
                onPressed: () => onClearFiltersPressed(context),
                fontSize: DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
                width: DeviceUtils().isTabletOrIpad(context) ? 90 : 200.w,
              ),
            ),
        ],
      ),
      drawer: isSmallScreen
          ? CustomDrawer(
        title: 'Cookbooks',
        state: cookbookState,
        buildContent: _buildCookbookList,
      )
          : null,
      body: Column(
        children: [
          Expanded(
            child: Row(
              children: [
                if (!isSmallScreen)
                  Expanded(
                    flex: 2,
                    child: _buildCookbookList(cookbookState),
                  ),
                Expanded(
                  flex: isSmallScreen ? 1 : 6,
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      Image.asset(
                        AssetsManager.background_img,
                        fit: BoxFit.cover,
                      ),
                      cookbookState.status == AppStatus.loading
                          ? RecipeShimmer(
                        crossAxisCount: crossAxisCount,
                        itemsPerPage:
                        DeviceUtils().isTabletOrIpad(context) ? 9 : 8,
                      )
                          : cookbookState.status == AppStatus.error
                          ? Center(
                          child: Text(
                              'Error: ${cookbookState.errorMessage}'))
                          : cookbookState.data == null ||
                          cookbookState.data!.isEmpty ||
                          _selectedCookbookIndex >=
                              cookbookState.data!.length
                          ? const Center(
                          child: NoDataWidget(
                            title: "No Cookbook Found Details",
                            subtitle:
                            "Try searching again or select a different cookbook to view the details.",
                            width: 250,
                            height: 250,
                          ))
                          : recipeState.status == AppStatus.loading
                          ? RecipeShimmer(
                        crossAxisCount: crossAxisCount,
                        itemsPerPage: DeviceUtils()
                            .isTabletOrIpad(context)
                            ? 9
                            : 8,
                      )
                          : recipeState.status == AppStatus.error
                          ? Center(
                          child: Text(
                              'Error: ${recipeState.errorMessage}'))
                          : PaginatedGridView(
                        crossAxisCount: crossAxisCount,
                        aspectRatio: 0.78,
                        selectedCookbook:
                        cookbookState.data![
                        _selectedCookbookIndex],
                        selectedCookbookIndex:
                        _selectedCookbookIndex,
                        cuisineId: cuisine,
                        categoryId: category,
                        searchQuery:
                        recipeSearchController.text
                            .trim(),
                        showAddRecipeCard: true,
                        currentPage: 1,
                        onPageChanged: (pageNumber) {},
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> onClearFiltersPressed(BuildContext context) async {
    setState(() {
      cuisine = 0;
      category = 0;
      _selectedFilters['Cuisine'] = '';
      _selectedFilters['Category'] = '';
      _selectedFilters['Sort'] = 'Sort By';
    });

    recipeSearchController.clear();
    cookbookSearchController.clear();

    ref.read(recipeMetadataProvider.notifier).resetFilters();
    bool isSuccess =
    await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
      context: context,
      loadMore: false,
    );

    if (isSuccess) {
      final cookbookState = ref.read(cookbookNotifierProvider);
      if (cookbookState.data != null && cookbookState.data!.isNotEmpty) {
        cookbookId = cookbookState.data![0].id;
        _selectedCookbookIndex = 0;
        fetchRecipe();
      } else {
        cookbookId = 0;
      }
    } else {
      cookbookId = 0;
    }
  }

  void fetchRecipe() {
     ref.read(recipeNotifierProvider.notifier).fetchRecipes(
      cookbookId: cookbookId,
      cuisineId: cuisine,
      categoryId: category,
      reset: true,
      context: context,
      sort: Utils().convertSortToApi(_selectedFilters['Sort']!),
      currentPage: 1,
    );
  }

  Widget _buildCookbookList(dynamic cookbookState) {
    return Column(
      children: [
        SizedBox(height: 10.h),
        CustomSearchBar(
          width: DeviceUtils().isTabletOrIpad(context) ? 368 : MediaQuery.of(context).size.width,
          controller: cookbookSearchController,
          onClear: () async {
            bool isSuccess = await ref
                .read(cookbookNotifierProvider.notifier)
                .fetchCookbooks(
              context: context,
              loadMore: false,
              search: '',
            );
            if (isSuccess) {
              final cookbookState = ref.read(cookbookNotifierProvider);
              if (cookbookState.data != null &&
                  cookbookState.data!.isNotEmpty) {
                cookbookId = cookbookState.data![0].id;
                _selectedCookbookIndex = 0;
                fetchRecipe();
              }
            }
          },
        ),
        Expanded(
          child: cookbookState.status == AppStatus.loading
              ? const CookbookListShimmer(
            itemCount: 20,
          )
              : cookbookState.status == AppStatus.error
              ? Center(
              child: Text('Error: ${cookbookState.errorMessage}'))
              : cookbookState.data == null || cookbookState.data!.isEmpty
              ? const Center(
              child: NoDataWidget(
                title: "No Cookbook Found",
                subtitle:
                "Try adjusting your search terms or create a new cookbook list",
                width: 250,
                height: 250,
              ))
              : ListView.separated(
            controller: _scrollController,
            padding: const EdgeInsets.all(12),
            itemCount: cookbookState.data!.length +
                (cookbookState.hasMore ? 1 : 0),
            separatorBuilder: (_, __) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              if (index >= cookbookState.data!.length) {
                return const Center(
                    child: CircularProgressIndicator());
              }
              final cookbook = cookbookState.data![index];
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedCookbookIndex = index;
                    cookbookId = cookbook.id;
                    cookbookName = cookbookState
                        .data![_selectedCookbookIndex].name;
                  });
                  fetchRecipe();
                  if (MediaQuery.of(context).size.width <= 1100) {
                    Navigator.of(context).pop();
                  }
                },
                child: CookbookDetailCard(
                  cookbook: cookbook,
                  isSelected: _selectedCookbookIndex == index,
                  onMenuItemSelected: (value) async {
                    if (value == 'Edit') {
                      ref
                          .read(cookbookNotifierProvider.notifier)
                          .resetToIdle();
                      showUpdateCookbookDialog(
                        context,
                        ref,
                        cookbookId: cookbook.id.toString(),
                        cookbookName: cookbook.name,
                      );
                    } else if (value == 'Delete') {
                      final cookbookNotifier =
                      ref.read(cookbookNotifierProvider.notifier);
                      final isDeleting = cookbookState.status == AppStatus.loading;
                      if (!isDeleting) {
                        var isSuccess = await cookbookNotifier
                            .deleteCookbook(
                            context, cookbook.id.toString());

                        if (isSuccess) {
                          cookbookId = cookbookState.data![1].id;
                          _selectedCookbookIndex = 0;
                          fetchRecipe();
                        }
                      }
                    }
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
