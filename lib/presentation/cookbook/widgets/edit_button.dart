import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

class EditButton extends StatelessWidget {
  final VoidCallback onPressed;
  const EditButton({super.key, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return TextButton.icon(
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      onPressed: onPressed,
      icon: SvgPicture.asset(AssetsManager.edit),
      label: Text("Edit",
          style: context.theme.textTheme.displaySmall!.copyWith(
            color: context.theme.hintColor,
            fontSize: 12
          )),
    );
  }
}
