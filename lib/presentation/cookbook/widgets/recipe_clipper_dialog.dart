import 'dart:async';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/providers/recipe_clipper_cookbook_notifier.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';

import '../../../app/imports/packages_imports.dart';
import '../../../core/network/app_status.dart';
import '../../../core/utils/Utils.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/clipper_drop_dpwn.dart';
import '../cookbook_screen.dart';

final cookbookNameProvider = StateProvider<String>((ref) => '');
final isCookbookCreatedProvider = StateProvider<bool>((ref) => false);
final importRecipeLoadingProvider = StateProvider<bool>((ref) => false);

class RecipeClipperDialog extends HookConsumerWidget {
  const RecipeClipperDialog({super.key});

  // Regular expression for basic URL validation
  static final RegExp _urlRegExp = RegExp(
    r'^(https?:\/\/)?' // Optional http or https
    r'((([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,})|' // Domain name (e.g., example.com)
    r'(localhost))' // or localhost
    r'(:\d+)?' // Optional port
    r'(\/[^\s]*)?$', // Optional path
    caseSensitive: false,
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final TextEditingController urlController = useTextEditingController();
    final TextEditingController descriptionController =
        useTextEditingController();
    final isDropdownOpen = useState<bool>(false);
    final isUrlValid = useState<bool>(true);
    final isUrlEmpty = useState<bool>(true);
    final selectedCookbook = useState<Cookbook?>(null);


    // Fetch cookbooks on dialog open
    useEffect(() {
      return () {
       // urlController.dispose();
       // descriptionController.dispose();
      };
    }, []);

    final cookbookState = ref.watch(recipeClipperCookbookNotifierProvider);
    final isImportLoading = ref.watch(importRecipeLoadingProvider);

    // Validate URL on change
    void validateUrl(String value) {
      isUrlEmpty.value = value.trim().isEmpty;
      isUrlValid.value =
          value.trim().isEmpty || _urlRegExp.hasMatch(value.trim());
    }

    // Clear controllers and reset state
    void clearControllers() {
     // urlController.clear();
     // descriptionController.clear();
     // isUrlEmpty.value = true;
      isUrlValid.value = true;
    //  selectedCookbook.value = null;
    }

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      elevation: 10,
      backgroundColor: Colors.white,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Container(
          color: Colors.white,
          padding: EdgeInsets.symmetric(horizontal: 30, vertical: 20),
          width: 800,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  icon: SvgPicture.asset(
                    AssetsManager.cross,
                    height: 20,
                    width: 20,
                  ),
                  onPressed: () {
                    clearControllers();
                    Navigator.pop(context);
                  },
                ),
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomText(
                    text: "Recipe Clipper",
                    color: AppColors.primaryGreyColor,
                    size: 26,
                    weight: FontWeight.w600,
                    overflow: TextOverflow.ellipsis,
                    underline: true, // Use updated CustomText with underline
                  ),
                ],
              ),
              SizedBox(height: 30),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 50),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                      text: "To Import recipe please enter Url",
                      color: AppColors.primaryGreyColor,
                      size: 20,
                      weight: FontWeight.w600,
                      overflow: TextOverflow.ellipsis,
                      underline: true,
                    ),
                    SizedBox(height: 20.h),
                    CustomInputField(
                      hintText: "Enter recipe URL",
                      controller: urlController,
                      onChanged: validateUrl,
                      maxLines: 3,
                    ),
                    if (!isUrlValid.value && !isUrlEmpty.value)
                      Padding(
                        padding: EdgeInsets.only(top: 8),
                        child: Text(
                          "Please enter a valid URL",
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: "Select Your Cookbook",
                          color: AppColors.primaryGreyColor,
                          size: 20,
                          weight: FontWeight.w600,
                          underline: true,
                        ),
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () {
                              if (!isDropdownOpen.value) {
                                showCookbookDialog(context, ref);
                              }
                            },
                            child: CustomText(
                              text: "+ Add New CookBook",
                              color: AppColors.primaryGreyColor,
                              size: 16,
                              weight: FontWeight.w600,
                              underline: true,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.h),
                    ClipperDropDpwn(
                      hint: "Search Cookbook",
                      selectedValue: selectedCookbook.value,
                      items: cookbookState.data ?? [],
                      cookbookState: cookbookState,
                      onChanged: (val) {
                        selectedCookbook.value = val;
                        print("Selected Cookbook: ${val?.id} - ${val?.name}");
                      },
                      onOpenStateChanged: (isOpen) {
                        isDropdownOpen.value = isOpen;
                      },
                      icon: SvgPicture.asset(
                        AssetsManager.search,
                        height: 15,
                        width: 15,
                      ),
                    ),
                    SizedBox(height: 20),
                    CustomText(
                      text: "Add a Note (Optional)",
                      color: AppColors.primaryGreyColor,
                      size: 14,
                      weight: FontWeight.w400,
                      overflow: TextOverflow.ellipsis,
                      underline: true,
                    ),
                    SizedBox(height: 20),
                    CustomInputField(
                      controller: descriptionController,
                      maxLines: 4,
                      hintText:
                          "E.g., reduce salt, try with tofu instead of chicken...",
                    ),
                    SizedBox(height: 40),
                    Center(
                      child: CustomButton(
                        text: 'Import Recipe',
                        height: 30,
                        isLoading: isImportLoading,
                        width: 250,
                        fontSize: DeviceUtils().isTabletOrIpad(context)
                            ? 16
                            : responsiveFont(18),
                        onPressed: () async {
                          if (urlController.text.trim().isEmpty) {
                            Utils().showFlushbar(
                              context,
                              message: 'Please enter a recipe URL',
                              isError: true,
                            );
                            isUrlEmpty.value = true;
                            return;
                          }
                          if (!_urlRegExp.hasMatch(urlController.text.trim())) {
                            Utils().showFlushbar(
                              context,
                              message: 'Please enter a valid URL',
                              isError: true,
                            );
                            isUrlValid.value = false;
                            return;
                          }
                          if (selectedCookbook.value == null) {
                            Utils().showFlushbar(
                              context,
                              message: 'Please select a cookbook',
                              isError: true,
                            );
                            return;
                          }

                          ref.read(importRecipeLoadingProvider.notifier).state =
                              true;

                          final result = await ref
                              .read(recipeClipperCookbookNotifierProvider.notifier)
                              .recipeClipper(
                                context,
                                urlController.text.trim(),
                                selectedCookbook.value,
                              );

                          ref.read(importRecipeLoadingProvider.notifier).state =
                              false;

                           if (result != null) {
                            var description = descriptionController.text.trim();
                            Navigator.pop(context);
                            await context.push(
                              '/cookbook/cookbookDetail/addRecipe',
                              extra: {
                                'selectedCookbook': selectedCookbook.value,
                                'callFromClipper': true,
                                'clipperNote': description,
                                'recipeDetails': result,
                              },
                            );
                            clearControllers();
                          }

                        },
                      ),
                    ),
                    SizedBox(height: 10.h),
                    Visibility(
                      visible: cookbookState.status == AppStatus.error,
                      child: Center(
                        child: TextButton(
                          onPressed: () async {
                            if (selectedCookbook.value == null) {
                              Utils().showFlushbar(
                                context,
                                message: 'Please select a cookbook',
                                isError: true,
                              );
                              return;
                            }
                            var description = descriptionController.text.trim();
                            Navigator.pop(context);
                            await context.push(
                              '/cookbook/cookbookDetail/addRecipe',
                              extra: {
                                'selectedCookbook': selectedCookbook.value,
                                'callFromClipper': false,
                                'clipperNote': description,
                              },
                            );
                            clearControllers();
                          },
                          child: CustomText(
                            text: 'Add Manually',
                            color: AppColors.primaryColor,
                            weight: FontWeight.w400,
                            decoration: TextDecoration.underline,
                            decorationColor: AppColors.primaryColor,
                            decorationThickness: 1.0,
                            size: 18,
                            underline: true,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 24),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
