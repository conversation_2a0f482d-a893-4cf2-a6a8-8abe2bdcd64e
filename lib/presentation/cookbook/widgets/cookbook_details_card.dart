// // import 'package:flutter/material.dart';
// // import 'package:flutter_screenutil/flutter_screenutil.dart';
// // import 'package:flutter_svg/svg.dart';
// // import 'package:mastercookai/app/imports/core_imports.dart';
// // import 'package:mastercookai/core/detail_subview/custom_text_medium.dart';
// // import '../../../../../core/models/cookbook.dart';
// // import '../../../../../core/utils/device_utils.dart';
// // import 'custom_desc_text.dart';
//
// // class CookbookDetailCard extends StatelessWidget {
// //   final Cookbook cookbook;
// //   final bool isSelected;
//
// //   const CookbookDetailCard({
// //     super.key,
// //     required this.cookbook,
// //     this.isSelected = false,
// //   });
//
// //   @override
// //   Widget build(BuildContext context) {
// //     final screenSize = MediaQuery.of(context).size;
// //     return Card(
// //       elevation: 2,
// //       color: context.theme.cardColor,
// //       shape: RoundedRectangleBorder(
// //         borderRadius: BorderRadius.circular(12),
// //         side: isSelected
// //             ? BorderSide(color: Colors.blue, width: 3)
// //             : BorderSide(color: AppColors.greyBorderColor, width: 1),
// //       ),
// //       child: Padding(
// //         padding: EdgeInsets.only(top: 20.sp , bottom: 20.sp, left: 20.sp , right: 20.sp),
// //         child: Row(
// //           crossAxisAlignment: CrossAxisAlignment.center,
// //           mainAxisAlignment: MainAxisAlignment.center,
// //           children: [
// //             // Image Section
// //             ClipRRect(
// //               borderRadius: BorderRadius.circular(8),
// //               child: Image.asset(
// //                 cookbook.coverImage,
// //                 width: 150.w,
// //                 height: 150.h,
// //                 fit: BoxFit.cover,
// //               ),
// //             ),
// //             SizedBox(width: 12.w),
// //             // Text and Action Section
// //             Expanded(
// //               child: Column(
// //                 crossAxisAlignment: CrossAxisAlignment.center,
// //                 mainAxisAlignment: MainAxisAlignment.center,
// //                 children: [
// //                   // SizedBox(height: 5.h,),
// //                   // Title and icons row
// //                   Row(
// //                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
// //                     crossAxisAlignment: CrossAxisAlignment.center,
// //                     children: [
// //                       Expanded(
// //                         child: Text(
// //                           cookbook.name,
// //                           style: context.theme.textTheme.bodyMedium!.copyWith(
// //                             color: AppColors.primaryGreyColor,
// //                             fontSize: responsiveFont(28).sp,
// //                             fontWeight: FontWeight.w400,
// //                           ),
// //                           maxLines: 1,
// //                           overflow: TextOverflow.ellipsis,
// //                         ),
// //                       ),
// //                       Row(
// //                         children: [
// //                           SvgPicture.asset(
// //                             AssetsManager.share,
// //                             height: 30.h,
// //                             width: 30.w,
// //                           ),
// //                           SizedBox(width: 14.w),
// //                           Icon(
// //                             Icons.more_vert,
// //                             color: AppColors.backgroudInActiveColor,
// //                             size: 30.h,
// //                           ),
// //                         ],
// //                       ),
// //                     ],
// //                   ),
//
// //                   SizedBox(height: 30.h),
//
// //                   // Footer: recipes count and creation date
// //                   Row(
// //                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
// //                     crossAxisAlignment: CrossAxisAlignment.end,
// //                     children: [
// //                       Row(
// //                         crossAxisAlignment: CrossAxisAlignment.start,
// //                         children: [
// //                           CustomTextMedium(title: "26 recipe's")
// //                           // CustomDescText(
// //                           //   desc: "26 recipe's",
// //                           //   size: 24.sp,
// //                           //   textColor: AppColors.textGreyColor,
// //                           // ),
// //                         ],
// //                       ),
// //                       SizedBox(
// //                         width: 20.w,
// //                       ),
// //                       Center(child: CustomTextMedium(title: "Created: 5 day's ago", size: responsiveFont(20).sp,))
// //                     ],
// //                   ),
// //                 ],
// //               ),
// //             ),
// //           ],
// //         ),
// //       ),
// //     );
// //   }
// // }
//
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:mastercookai/app/imports/core_imports.dart';
//  import 'package:mastercookai/core/widgets/custom_text_medium.dart';
// import 'package:mastercookai/presentation/cookbook/widgets/update_cookbook_dialog.dart';
// import 'package:timeago/timeago.dart' as timeago;
// import '../../../../../core/utils/device_utils.dart';
// import '../../../../core/data/models/cookbook.dart';
// import '../../../app/imports/packages_imports.dart';
// import '../../../core/helpers/share_utils.dart';
// import '../../../core/network/app_status.dart';
// import '../../../core/providers/cookbook_notifier.dart';
// import '../../../core/widgets/common_image.dart';
// import '../../../core/widgets/custom_hover_menu.dart';
// import 'custom_desc_text.dart';
//
// class CookbookDetailCard extends ConsumerWidget {
//   final Cookbook cookbook;
//   final bool isSelected;
//
//
//   const CookbookDetailCard({
//     super.key,
//     required this.cookbook,
//     this.isSelected = false,
//   });
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final cookbookState = ref.watch(cookbookNotifierProvider);
//     final cookbookNotifier = ref.read(cookbookNotifierProvider.notifier);
//     final isDeleting = cookbookState.status == AppStatus.loading;
//
//     return Card(
//       elevation: 2,
//       color: context.theme.cardColor,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(12),
//         side: isSelected
//             ? BorderSide(color: Colors.blue, width: 3)
//             : BorderSide(color: AppColors.greyBorderColor, width: 1),
//       ),
//       child: Padding(
//         padding: EdgeInsets.only(
//           top: 20.sp,
//           bottom: 20.sp,
//           left: 20.sp,
//           right: 20.sp,
//         ),
//         child: Row(
//           crossAxisAlignment: CrossAxisAlignment.center,
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             // Image Section
//             ClipRRect(
//               borderRadius: BorderRadius.circular(8),
//               child: CommonImage(
//                 imageSource: cookbook.coverImageUrl,
//                 // or File or asset path
//                 height: 150.h,
//                 width: 150.w,
//                 fit: BoxFit.cover,
//               ),
//             ),
//             SizedBox(width: 12.w),
//             // Text and Action Section
//             Expanded(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   // Title and icons row
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     crossAxisAlignment: CrossAxisAlignment.center,
//                     children: [
//                       Expanded(
//                         child: Text(
//                           cookbook.name,
//                           style: context.theme.textTheme.bodyMedium!.copyWith(
//                             color: AppColors.primaryGreyColor,
//                             fontSize: responsiveFont(28).sp,
//                             fontWeight: FontWeight.w400,
//                           ),
//                           maxLines: 1,
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       ),
//
//                       CustomHoverMenu(
//                         items: ['Edit',  'Delete'],
//                         itemIcons: [Icons.edit, Icons.delete], // Icons for each item
//                         onItemSelected: (value) {
//                           if (value == 'Edit') {
//                             ref.read(cookbookNotifierProvider.notifier).resetToIdle();
//                             showUpdateCookbookDialog(
//                               context,
//                               ref,
//                               cookbookId: cookbook.id.toString(),
//                               cookbookName: cookbook.name,
//                             );
//                           } else if (value == 'Delete') {
//                             if (!isDeleting) {
//                               cookbookNotifier.deleteCookbook(context, cookbook.id.toString());
//
//                             }
//                           }
//                         },
//                         menuWidth: 140.0, // Matches previous menu width
//                         menuTitle: 'Show Menu', // Tooltip on hover
//                         triggerIcon: Icons.more_vert_outlined, // Custom trigger icon
//                       ),
//
//                     ],
//                   ),
//
//                   SizedBox(height: 30.h),
//
//                   // Footer: recipes count and creation date
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     crossAxisAlignment: CrossAxisAlignment.end,
//                     children: [
//                       CustomTextMedium(
//                         title: "${cookbook.recipeCount} recipes",
//                         size: responsiveFont(22).sp,
//                         textColor: AppColors.textGreyColor,
//                       ),
//                       CustomTextMedium(
//                         title:
//                             "Created: ${timeago.format(cookbook.dateAdded, locale: 'en_short')}",
//                         size: responsiveFont(22).sp,
//                         textColor: AppColors.textGreyColor,
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
//
//


import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_text_medium.dart';
import 'package:mastercookai/presentation/cookbook/widgets/update_cookbook_dialog.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
 import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/cookbook_notifier.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import 'package:mastercookai/core/widgets/custom_hover_menu.dart';

class CookbookDetailCard extends ConsumerStatefulWidget {
  final Cookbook cookbook;
  final bool isSelected;
  final void Function(String) onMenuItemSelected; // Callback for menu item selection

  const CookbookDetailCard({
    super.key,
    required this.cookbook,
    this.isSelected = false,
    required this.onMenuItemSelected,
  });

  @override
  ConsumerState<CookbookDetailCard> createState() => _CookbookDetailCardState();
}

class _CookbookDetailCardState extends ConsumerState<CookbookDetailCard> {
  String? _selectedItem;

  @override
  Widget build(BuildContext context) {
    final cookbookState = ref.watch(cookbookNotifierProvider);
    final cookbookNotifier = ref.read(cookbookNotifierProvider.notifier);
    final isDeleting = cookbookState.status == AppStatus.loading;

    return Card(
      elevation: 2,
      color: context.theme.cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: widget.isSelected
            ? BorderSide(color: Colors.blue, width: 3)
            : BorderSide(color: AppColors.greyBorderColor, width: 1),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          top: 20.sp,
          bottom: 20.sp,
          left: 20.sp,
          right: 20.sp,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Image Section
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CommonImage(
                imageSource: widget.cookbook.coverImageUrl,
                placeholder: AssetsManager.cb_place_holder,
                height: 90,
                width: 90,
                fit: BoxFit.cover,
              ),
            ),
            SizedBox(width: 12.w),
            // Text and Action Section
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Title and icons row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Text(
                          widget.cookbook.name,
                          style: context.theme.textTheme.bodyMedium!.copyWith(
                            color: AppColors.primaryGreyColor,
                            fontSize: 16,//responsiveFont(28).sp,
                            fontWeight: FontWeight.w400,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      CustomHoverMenu(
                        items: ['Edit',   'Delete'],
                        itemIcons: [Icons.edit, Icons.delete],
                        onItemSelected:widget.onMenuItemSelected,
                        //     (value) {
                        //   setState(() {
                        //     _selectedItem = value;
                        //   });
                        //   // Call parent callback with menu item and cookbook
                        //   widget.onMenuItemSelected(value, widget.cookbook);
                        //   print('Selected: $value');
                        //   // Local handling for Edit and Delete
                        //   if (value == 'Edit') {
                        //     ref.read(cookbookNotifierProvider.notifier).resetToIdle();
                        //     showUpdateCookbookDialog(
                        //       context,
                        //       ref,
                        //       cookbookId: widget.cookbook.id.toString(),
                        //       cookbookName: widget.cookbook.name,
                        //     );
                        //   } else if (value == 'Delete') {
                        //     if (!isDeleting) {
                        //       cookbookNotifier.deleteCookbook(context, widget.cookbook.id.toString());
                        //     }
                        //   }
                        // },
                        menuWidth: 140.0,
                        menuTitle: 'Show Menu',
                        triggerIcon: Icons.more_vert_outlined,
                        selectedItem: _selectedItem,
                      ),
                    ],
                  ),
                  SizedBox(height: 30.h),
                  // Footer: recipes count and creation date
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      CustomTextMedium(
                        title: "${widget.cookbook.recipeCount} recipes",
                        size: 14,//responsiveFont(22).sp,
                        textColor: AppColors.textGreyColor,
                      ),
                      CustomTextMedium(
                        title:
                        "Created: ${timeago.format(widget.cookbook.dateAdded, locale: 'en_short').replaceAll('just now ago', 'just now')}",
                        size: 14,//responsiveFont(22).sp,
                        textColor: AppColors.textGreyColor,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}