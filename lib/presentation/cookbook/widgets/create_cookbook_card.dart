import 'dart:ui';

import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../../../../app/imports/packages_imports.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../core/utils/device_utils.dart';
import '../../recipe/subview/custom_sub_media_widget.dart';

class ImportCreateCard extends StatelessWidget {
  final VoidCallback onImport;
  final VoidCallback? onAddFromUrl;
  final VoidCallback onCreate;
  final bool isRecipe;
  final String title;
  bool? isAddRecipe;

  final String importText;

  ImportCreateCard(
      {super.key,
      required this.onImport,
      this.onAddFromUrl,
      required this.onCreate,
      required this.isRecipe,
      required this.title,
      this.isAddRecipe = false,
      required this.importText});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Card(
        elevation: 0,
        color: context.theme.cardColor,
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: Padding(
          padding:
              EdgeInsets.only(left: 30.w, right: 30.w, top:DeviceUtils().isTabletOrIpad(context)?40.h: 80.h, bottom:DeviceUtils().isTabletOrIpad(context)?40.h: 80.h),
          child: CustomPaint(
            painter: DottedBorderPainter(),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                      onTap: onImport,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: DeviceUtils().isTabletOrIpad(context)
                                  ? 4 : 20.0, vertical: DeviceUtils().isTabletOrIpad(context)
                                  ? 10 : 12.0,),
                        // Adjust padding as needed
                        decoration: BoxDecoration(
                          color: Colors.white,
                          // Background color of the button
                          borderRadius: BorderRadius.circular(10.0),
                          // Rounded corners
                          border: Border.all(
                            color: AppColors.lightestGreyColor, // Border color
                            width: 1.5,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          // To make the row only take up necessary space
                          children: [
                            SvgPicture.asset(
                              AssetsManager.mynaui_file,
                              height: DeviceUtils().isTabletOrIpad(context)
                                  ? 28
                                  : 24, // Adjust height as needed
                              width: DeviceUtils().isTabletOrIpad(context)
                                  ? 28
                                  : 24, // Adjust width as needed
                              colorFilter: ColorFilter.mode(Colors.red,
                                  BlendMode.srcIn), // Apply color to SVG
                              // Alternatively, if your SVG already has the desired color and you don't want to override it:
                              // color: null, // Remove colorFilter if the SVG has its own color and you want to keep it
                            ),

                            SizedBox(width: DeviceUtils().isTabletOrIpad(context)
                                  ? 2 : 8.0),
                            CustomText(
                              text: importText,
                              color: AppColors.primaryGreyColor,
                              size: 14,
                              weight: FontWeight.w400,
                            ),

                          ],
                        ),
                      )),
                ),
                SizedBox(
                  height: 10.h,
                ),
                Visibility(
                  visible: isAddRecipe ?? false,
                  child: Column(
                    children: [
                      SizedBox(
                        height: 20.h,
                      ),
                      // Import Button
                      MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: GestureDetector(
                            onTap: onAddFromUrl,
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal:  DeviceUtils().isTabletOrIpad(context)
                                  ? 4 : 20.0, vertical:  DeviceUtils().isTabletOrIpad(context)
                                  ? 10 : 12.0),
                              // Adjust padding as needed
                              decoration: BoxDecoration(
                                color: Colors.white,
                                // Background color of the button
                                borderRadius: BorderRadius.circular(10.0),
                                // Rounded corners
                                border: Border.all(
                                  color: AppColors.lightestGreyColor,
                                  // Border color
                                  width: 1.5,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                // To make the row only take up necessary space
                                children: [
                                  SvgPicture.asset(
                                    AssetsManager.add_link,
                                    height:
                                        DeviceUtils().isTabletOrIpad(context)
                                            ? 28
                                            : 24, // Adjust height as needed
                                    width: DeviceUtils().isTabletOrIpad(context)
                                        ? 28
                                        : 24, // Adjust width as needed
                                    colorFilter: ColorFilter.mode(Colors.red,
                                        BlendMode.srcIn), // Apply color to SVG
                                    // Alternatively, if your SVG already has the desired color and you don't want to override it:
                                    // color: null, // Remove colorFilter if the SVG has its own color and you want to keep it
                                  ),

                                  SizedBox(width: DeviceUtils().isTabletOrIpad(context)
                                  ? 2 : 8.0),
                                  // Space between icon and text
                                  CustomText(
                                    text: 'Import From URL',
                                    color: AppColors.primaryGreyColor,
                                    size: 14,
                                    weight: FontWeight.w400,
                                  ),
                                ],
                              ),
                            )),
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                    ],
                  ),
                ),

                SizedBox(
                  height: 30.h,
                ),
                Padding(
                  padding: EdgeInsets.only(left: 40.w, right: 40.w),
                  child: CustomButton(
                      text: title,
                      fontSize:
                          DeviceUtils().isTabletOrIpad(context) ? 14 : 20.sp,
                      onPressed: onCreate),
                ),

                // Create Button
              ],
            ),
          ),
        ),
      ),
    );
  }
}
