import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/core/helpers/local_storage_service.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/presentation/profile/sub_view/profile_body.dart';
import 'package:mastercookai/presentation/profile/sub_view/profile_header.dart';
import '../../app/assets_manager.dart';
import '../../core/providers/profile/user_profile_notifier.dart';
import '../../core/providers/profile/user_types_notifier.dart';
import '../../core/widgets/custom_appbar.dart' show CustomAppBar;

class myAccount extends ConsumerStatefulWidget {
  const myAccount({super.key});

  @override
  ConsumerState<myAccount> createState() => _myAccountState();
}

class _myAccountState extends ConsumerState<myAccount> with SingleTickerProviderStateMixin {


  @override
  void initState() {
    super.initState();

    // Fetch user profile and user types data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
      ref.read(userTypesNotifierProvider.notifier).fetchUserTypes();
    });
  }

  @override
  Widget build(BuildContext context) {
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final userProfileNotifier = ref.read(userProfileNotifierProvider.notifier);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'My Account',
        actions: [
          GestureDetector(
            onTap: () async {
              final bool? confirmed =
              await Utils().showCommonConfirmDialog(
                context: context,
                title: 'Logout',
                subtitle: 'Are you sure you want to Logout?',
                confirmText: 'Logout',
                cancelText: 'Cancel',
              );
              if (confirmed == true && context.mounted) {
                context.go('/splash');
                final localStorage = ref.read(localStorageProvider);
                await localStorage.clearLoginData();
              }
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              child: Image.asset(
                AssetsManager.logoutImage,
                width: 40,
                height: 40,
                fit: BoxFit.cover,
              ),
            ),

          ),
          const SizedBox(width: 26)
        ],
      ),
      body: Stack( // Use Stack to layer widgets
        children: [
          // Background Image
          Positioned.fill( // Ensures the image fills the entire available space
            child: Image.asset(
                AssetsManager.background_img,
                fit: BoxFit.cover,
            ),
          ),
          // Your existing content, layered on top of the background image
          Column(
            children: [
              ProfileHeader(
                  userProfileNotifier: userProfileNotifier, userProfileState: userProfileState),
              Expanded( // Keep Expanded here if profileBody needs to take remaining vertical space
                child: profileBody(
                    context: context,
                    userProfileState:userProfileState
                ),
              ),
            ],
          ),
        ],
      ),


    );
  }
}
