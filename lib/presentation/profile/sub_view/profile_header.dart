import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/data/models/user_profile_response.dart';
import 'package:mastercookai/core/data/request_query/update_profile_request.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/core/widgets/image_cropper.dart';

import '../../../app/imports/core_imports.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../app/theme/colors.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/helpers/media_picker_service.dart';
import '../../../core/network/app_status.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/common_image.dart';

class ProfileHeader extends ConsumerStatefulWidget {
  final UserProfileNotifier userProfileNotifier;
  final AppState<UserProfileData?> userProfileState;

  const ProfileHeader({
    super.key,
    required this.userProfileNotifier,
    required this.userProfileState,
  });

  @override
  ConsumerState<ProfileHeader> createState() => _ProfileHeaderState();
}

class _ProfileHeaderState extends ConsumerState<ProfileHeader> {
  File? selectedProfileImage;

  Future<void> _updateProfile() async {
    try {
      final userProfile = ref.read(userProfileNotifierProvider).data!.userProfile!;
      final request = UpdateProfileRequest(
        firstName: userProfile.name.split(' ').first,
        lastName: userProfile.name.split(' ').length > 1 ? userProfile.name.split(' ').sublist(1).join(' ') : '',
        gender: userProfile.gender,
        dob: userProfile.dob,
        countryCode: '+1', // This should be fetched from user's data if available
        contact: userProfile.phone,
        companyName: userProfile.companyName,
        userTypeId: userProfile.userTypeId,
        profilePic: selectedProfileImage,
      );

      final success = await ref
          .read(userProfileNotifierProvider.notifier)
          .updateProfile(context: context, request: request);

      if (success && mounted) {
        await ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
        setState(() {
          selectedProfileImage = null;
        });
      }
    } catch (e) {
      if (mounted) {
        Utils().showFlushbar(context,
            message: 'Failed to update profile: $e', isError: true);
      }
    }
  }

  void _pickProfileImage() async {
    final file = await MediaPickerService.pickSingleImage();
    if (file != null && mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ImageCropper(
            pickedImage: file,
            showCropPresets: true,
            showGridLines: true,
            useDelegate: true,
            enableFreeformCrop: true,
            onImageCropped: (File? croppedImageFile) {
              if (croppedImageFile != null) {
                setState(() {
                  selectedProfileImage = croppedImageFile;
                });
                _updateProfile();
              }
            },
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: 20.0,
          horizontal: DeviceUtils().isTabletOrIpad(context) ? 20 : 100.0),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.whiteColor,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              spreadRadius: 2,
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // Profile picture and details
            Row(
              children: [
                SizedBox(
                  width: 100,
                  height: 100,
                  child: Stack(
                    children: [
                      ClipOval(
                        child: selectedProfileImage != null
                            ? Image.file(
                                selectedProfileImage!,
                                width: 100,
                                height: 100,
                                fit: BoxFit.cover,
                              )
                            : widget.userProfileNotifier.hasProfilePic
                                ? CommonImage(
                                    imageSource:
                                        widget.userProfileNotifier.profilePicUrl!,
                                    width: 100,
                                    height: 100,
                                    fit: BoxFit.cover,
                                    placeholder: AssetsManager.addProfile,
                                  )
                                : Container(
                                    width: 100,
                                    height: 100,
                                    color: Colors.grey.shade300,
                                    child: const Icon(Icons.person,
                                        color: Colors.grey, size: 50),
                                  ),
                      ),
                      if (widget.userProfileState.status == AppStatus.loading)
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.3),
                            shape: BoxShape.circle,
                          ),
                        ),
                      // Add the edit icon specifically for tablets
                      if (DeviceUtils().isTabletOrIpad(context))
                        Positioned(
                          top: 0,
                          right: 0,
                          child: GestureDetector(
                            onTap: () {
                              if (selectedProfileImage?.path != null) {
                                _updateProfile();
                              } else {
                                _pickProfileImage();
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: AppColors
                                    .primaryColor, // Use your desired color
                                shape: BoxShape.circle,
                                border:
                                    Border.all(color: Colors.white, width: 2),
                              ),
                              child: const Icon(
                                Icons.edit,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                const SizedBox(width: 20),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Image.asset(
                          AssetsManager.personImage,
                          width: 20,
                          height: 20,
                          fit: BoxFit.contain,
                        ),
                        const SizedBox(width: 10),
                        CustomText(
                            text: widget.userProfileNotifier.userName.isNotEmpty
                                ? widget.userProfileNotifier.userName
                                : 'Loading...',
                            size: DeviceUtils().isTabletOrIpad(context)
                                ? 14
                                : responsiveFont(24).sp,
                            weight: FontWeight.w600,
                            color: AppColors.primaryGreyColor),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        Icon(Icons.mail,
                            color: AppColors.primaryColor, size: 20),
                        SizedBox(width: 10),
                        CustomText(
                            text: widget.userProfileNotifier.userEmail.isNotEmpty
                                ? widget.userProfileNotifier.userEmail
                                : 'Loading...',
                            size: DeviceUtils().isTabletOrIpad(context)
                                ? 14
                                : responsiveFont(22).sp,
                            weight: FontWeight.w400,
                            color: AppColors.primaryGreyColor),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        Icon(Icons.phone,
                            color: AppColors.primaryColor, size: 20),
                        SizedBox(width: 10),
                        CustomText(
                            text: widget.userProfileNotifier.userProfile?.phone
                                        .isNotEmpty ==
                                    true
                                ? widget.userProfileNotifier.userProfile!.phone
                                : 'Loading...',
                            size: DeviceUtils().isTabletOrIpad(context)
                                ? 14
                                : responsiveFont(22).sp,
                            weight: FontWeight.w400,
                            color: AppColors.primaryGreyColor),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            const Spacer(),

            Container(
              width: 1,
              height: 90,
              color: Colors.grey.shade300,
            ),
            const Spacer(),
            // Storage section
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      AssetsManager.cloud,
                      color: Color(0xFF007AFF),
                      width: 24,
                      height: 24,
                    ),
                    const SizedBox(width: 8),
                    CustomText(
                      text: 'Storage',
                      weight: FontWeight.w800,
                      size: DeviceUtils().isTabletOrIpad(context)
                          ? 14
                          : responsiveFont(28).sp,
                      color: AppColors.primaryGreyColor,
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Consumer(
                  builder: (context, ref, child) {
                    final userProfileState =
                        ref.watch(userProfileNotifierProvider);

                    if (userProfileState.status == AppStatus.success &&
                        userProfileState.data?.userProfile != null) {
                      final userProfile = userProfileState.data!.userProfile!;

                      // Format used storage - show MB if less than 1GB, otherwise show GB
                      String usedDisplay;
                      if (userProfile.storageUsedInMB < 1024) {
                        usedDisplay = '${userProfile.storageUsedInMB}MB';
                      } else {
                        final usedGB = (userProfile.storageUsedInMB / 1024)
                            .toStringAsFixed(0);
                        usedDisplay = '${usedGB}GB';
                      }

                      final totalGB = (userProfile.storageQuotaInMB / 1024)
                          .toStringAsFixed(0);

                      return Column(
                        children: [
                          CustomText(
                            text: usedDisplay,
                            size: responsiveFont(50).sp,
                            weight: FontWeight.w900,
                            color: AppColors.primaryColor,
                          ),
                          const SizedBox(height: 5),
                          CustomText(
                            text: 'used of ${totalGB}GB',
                            size: DeviceUtils().isTabletOrIpad(context)
                                ? 12
                                : responsiveFont(22).sp,
                            fontFamily: 'inter',
                            weight: FontWeight.w400,
                            color: AppColors.primaryGreyColor,
                            align: TextAlign.center,
                          ),
                        ],
                      );
                    }

                    // Fallback to static data (using actual API values)
                    return Column(
                      children: [
                        CustomText(
                          text: '137MB',
                          size: responsiveFont(50).sp,
                          weight: FontWeight.w900,
                          color: AppColors.primaryColor,
                        ),
                        const SizedBox(height: 5),
                        CustomText(
                          text: 'used of 5GB',
                          size: responsiveFont(22).sp,
                          weight: FontWeight.w400,
                          color: AppColors.primaryGreyColor,
                          align: TextAlign.center,
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }
}

// Helper method to build billing text
String _buildBillingText(CurrentPlan? currentPlan, BuildContext context) {
  final billingInterval = currentPlan?.billingInterval ?? 'year';
  final billingDisplay =
      billingInterval.toLowerCase() == 'year' ? 'Yearly' : billingInterval;
  final nextPaymentDate =
      Utils().formatDate(currentPlan?.nextPaymentDate ?? '2026-06-16');
  final planAmount = currentPlan?.planAmount ?? 70;

  if (DeviceUtils().isTabletOrIpad(context)) {
    return 'Billed $billingDisplay\nNext payment on\n $nextPaymentDate\nFor \$$planAmount';
  } else {
    return 'Billed $billingDisplay\nNext payment on $nextPaymentDate for \$$planAmount';
  }
}
