import 'package:mastercookai/app/imports/core_imports.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/providers/profile/plans_notifier.dart';
import '../../../core/providers/profile/user_profile_notifier.dart';
import '../../../core/network/app_status.dart';
import '../../../core/data/models/plans_response.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/custom_text.dart';

class SubscriptionView extends ConsumerStatefulWidget {
  const SubscriptionView({super.key});

  @override
  ConsumerState<SubscriptionView> createState() => _SubscriptionViewState();
}

class _SubscriptionViewState extends ConsumerState<SubscriptionView> {
  @override
  void initState() {
    super.initState();
    // Fetch plans when the widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(plansNotifierProvider.notifier).fetchPlans();
    });
  }

  // Helper method to check if a feature is available for a plan
  bool _isFeatureAvailable(Plan plan, String featureName) {
    return plan.features?.any((feature) =>
    feature.featureName
        ?.toLowerCase()
        .contains(featureName.toLowerCase()) ??
        false) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    final plansState = ref.watch(plansNotifierProvider);

    return Column(
      children: [
        // Show loading state
        if (plansState.status == AppStatus.loading)
          const Expanded(
            child: Center(
              child: CircularProgressIndicator(
                color: AppColors.primaryColor,
              ),
            ),
          )
        // Show error state
        else if (plansState.status == AppStatus.error)
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load plans',
                    style: TextStyle(
                      fontSize: responsiveFont(20).sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  CustomText(
                    text:  plansState.errorMessage ?? 'Unknown error occurred',
                    size: 14,
                    color: Colors.grey[500],
                    align: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      ref.read(plansNotifierProvider.notifier).fetchPlans();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                    ),
                    child: const Text(
                      'Retry',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
            ),
          )
        // Show plans data
        else if (plansState.data?.plans != null &&
              plansState.data!.plans!.isNotEmpty)
            ..._buildPlansContent(plansState.data!.plans!)
          // Show empty state
          else
            const Expanded(
              child: Center(
                child: CustomText(
                  text:   'No plans available',
                  size: 18,
                  color: Colors.grey,
                ),
              ),
            ),
      ],
    );
  }

  List<Widget> _buildPlansContent(List<Plan> plans) {
    // Get current plan name from user profile
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final currentPlanName =
        userProfileState.data?.userProfile?.currentPlan?.planName;

    // Get all unique features from all plans
    final Set<String> allFeatures = <String>{};
    for (final plan in plans) {
      for (final feature in plan.features ?? []) {
        if (feature.featureName != null) {
          allFeatures.add(feature.featureName!);
        }
      }
    }
    final List<String> featuresList = allFeatures.toList();

    return [
      // Column headers
      Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 42, top: 20),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: CustomText(
                text:    'Features',
                weight: FontWeight.w500,
                size: 16,
                color: Colors.grey[700],
              ),
            ),
            for (int i = 0; i < plans.length; i++)
              Expanded(
                child: Container(
                  alignment: Alignment.center,
                  child: Column(
                    children: [
                      Stack(
                        alignment: Alignment.topCenter,
                        children: [
                          if (plans[i].planName == currentPlanName)
                            Positioned(
                              top: 0,
                              child: Text(
                                "", //Current Plan
                                style: TextStyle(
                                  fontSize: responsiveFont(20),
                                  fontWeight: FontWeight.w600,
                                  color: Colors.red,
                                ),
                              ),
                            ),
                          if (i == 2) // Popular Plan (Premium Plan)
                            Positioned(
                              top: 0,
                              child: Container(
                                color: Colors.red,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 15, vertical: 2),
                                child:CustomText(
                                  text:   "POPULAR",
                                  size: 16,
                                  weight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          Padding(
                            padding: const EdgeInsets.only(top: 30),
                            child: CustomText(
                              text: '${plans[i].planName ?? 'Unknown Plan'} Plan',
                              align: TextAlign.center,
                              size: 16,
                              weight: FontWeight.w500,
                              color: AppColors.primaryGreyColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),

      // List of features (scrollable)
      Expanded(
        child: ListView.builder(
          padding: const EdgeInsets.only(bottom: 30),
          itemCount: featuresList.length,
          itemBuilder: (context, index) {
            return Container(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 42),
              decoration: BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      featuresList[index],
                      style: TextStyle(
                        fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : responsiveFont(21).sp,
                        fontFamily: 'inter',
                        fontWeight: FontWeight.w500,
                        color: AppColors.primaryGreyColor,
                      ),
                    ),
                  ),
                  for (int i = 0; i < plans.length; i++)
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.only(left: 36.w),
                        alignment: Alignment.center,
                        child: Image.asset(
                          _isFeatureAvailable(plans[i], featuresList[index])
                              ? AssetsManager.featureAvailable
                              : AssetsManager.featureNotAvailable,
                          width: 13,
                          height: 13,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        ),
      ),

      // Fixed bottom section with Update Plan buttons
      Container(
        color: Colors.white,
        padding: const EdgeInsets.only(
            top: 10.0, right: 20.0, left: 20.0, bottom: 16.0),
        child: Row(
          children: [
            Container(width: 1, color: Colors.grey[200]),
            SizedBox(width: MediaQuery.of(context).size.width * (DeviceUtils().isTabletOrIpad(context)? 0.2:0.4)),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  for (int i = 0; i < plans.length; i++) ...[
                    if (i > 0) const SizedBox(width: 20),
                    Expanded(
                      child: FreemiumPlanButton(
                        currentPlan: i == 0
                            ? 'Downgrade'
                            : i == 1
                            ? 'Active Plan'
                            : 'Upgrade',
                        priceText: plans[i].yearlyFee != null
                            ? '\$${plans[i].yearlyFee}'
                            : 'Free',

                        billingCycleText: plans[i].yearlyFee != null
                            ? '/Yearly'
                            : '/Lifetime',
                        planNameText: '${plans[i].planName ?? 'Unknown Plan'} Plan',
                        isCurrentPlan: plans[i].planName == currentPlanName, // Add this line
                        onPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                                content: Text(
                                    '${plans[i].planName} button tapped!')),
                          );
                        },
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    ];
  }
}

class FreemiumPlanButton extends StatelessWidget {
  final String currentPlan;
  final String priceText;
  final String billingCycleText;
  final String planNameText;
  final VoidCallback? onPressed;
  final bool isPopular;
  final bool isCurrentPlan;

  const FreemiumPlanButton({
    super.key,
    required this.currentPlan,
    required this.priceText,
    required this.billingCycleText,
    required this.planNameText,
    this.onPressed,
    this.isPopular = false,
    this.isCurrentPlan = false,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: currentPlan=='Active Plan' ? Colors.green : Colors.grey[200],
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6.0),
          side: BorderSide(
            color: currentPlan=='Active Plan' ? Colors.green : Colors.grey[300]!,
            width: 1.0,
          ),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomText(
                text: priceText,
                size: 20,
                weight: FontWeight.w600,
                color: currentPlan=='Active Plan' ? Colors.white : AppColors.primaryColor,
              ),
              CustomText(
                text: billingCycleText,
                size: 15,
                weight: FontWeight.w400,
                color: currentPlan=='Active Plan' ? Colors.white : AppColors.textGreyColor,
              ),
            ],
          ),
          CustomText(
            text: currentPlan,
            size: 15,
            weight: FontWeight.w600,
            color: currentPlan=='Active Plan' ? Colors.white : AppColors.primaryGreyColor,
            align: TextAlign.center,
          ),
        ],
      ),
    );
  }
}