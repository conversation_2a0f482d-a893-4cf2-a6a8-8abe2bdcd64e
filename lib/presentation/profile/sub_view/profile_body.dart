import 'package:flutter/cupertino.dart';
import 'package:mastercookai/core/data/models/user_profile_response.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/presentation/profile/sub_view/profile_view.dart';
import 'package:mastercookai/presentation/profile/sub_view/setting_view.dart';
import 'package:mastercookai/presentation/profile/sub_view/storage_view.dart';
import 'package:mastercookai/presentation/profile/sub_view/subscription_view.dart';
import 'package:mastercookai/presentation/shopping/sub_view/custom_tabview.dart';
import '../../../app/imports/core_imports.dart';
import '../../../core/utils/device_utils.dart';

Widget profileBody({
  required BuildContext context,
  required AppState<UserProfileData?> userProfileState,
}) {

  return Expanded(
    child: Padding(
      padding: EdgeInsets.only(
        left: DeviceUtils().isTabletOrIpad(context) ? 20 : 100.0,
        right: DeviceUtils().isTabletOrIpad(context) ? 20 : 100.0,
        bottom: 140.0,
      ),
      child: DefaultTabController(
        length: 3,
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: AppColors.primaryGreyColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: TabBar(
                onTap: (index) {
                  print("Tab tapped at index: $index");
                 },
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  color: AppColors.whiteColor,
                  border: Border(
                    bottom: BorderSide(
                      color: AppColors.primaryGreyColor,
                      width: 3.0,
                    ),
                  ),
                ),
                indicatorSize: TabBarIndicatorSize.tab,
                labelColor: AppColors.primaryGreyColor,
                unselectedLabelColor: const Color.fromARGB(57, 211, 209, 209),
                tabs: [
                  Tab(
                    child: Text(
                      'Profile',
                       style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: responsiveFont(20),
                      ),
                    ),
                  ),
                  // Visibility(
                  //   visible: false,
                  //   child: Tab(
                  //    child: Text(
                  //       'Subscription',
                  //       style: TextStyle(
                  //         fontWeight: FontWeight.w600,
                  //         fontSize: responsiveFont(20),
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  Tab(
                    child: Text(
                      'Storage',
                       style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: responsiveFont(20),
                      ),
                    ),
                  ),
                  Tab(
                    child: Text(
                      'Setting',
                       style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: responsiveFont(20),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Container(
                clipBehavior: Clip.none,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(20),
                    bottomRight: Radius.circular(20),
                  ),
                  color: AppColors.whiteColor,
                ),
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    TabBarView(
                      children: [
                        // Profile Tab Content with callback to handle index
                        ProfileView(

                         ),
                        // Subscription Tab Content
                      //  const SubscriptionView(),
                       // Storage Tab Content
                        const StorageView(),
                        // Settings Tab Content
                        SettingView(),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
