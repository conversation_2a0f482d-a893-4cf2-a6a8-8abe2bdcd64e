import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import '../../../core/providers/profile/user_profile_notifier.dart';
import '../../../core/widgets/custom_appbar.dart';

class MyAccountMobileUI extends ConsumerWidget {
  const MyAccountMobileUI({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final userProfile = userProfileState.data?.userProfile;

    return Scaffold(
      backgroundColor: AppColors.whiteColor,
      appBar: CustomAppBar(
        title: 'My Account',
        onPressed: () => context.pop(),
        actions: [
          Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              onPressed: () {
                // Settings action
              },
              icon: const Icon(
                Icons.settings,
                color: AppColors.primaryGreyColor,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                  color: const Color(0x1AE0E0E0),
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      spreadRadius: 1,
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                  border: Border.all(
                    color: const Color(0xFFE0E0E0),
                    width: 1,
                  )),
              child: Column(
                children: [
                  // Profile Picture with verified badge
                  Stack(
                    children: [
                      CircleAvatar(
                        radius: 40,
                        backgroundColor: Colors.grey.shade200,
                        backgroundImage: userProfile?.profilePic != null
                            ? NetworkImage(userProfile!.profilePic!)
                            : const AssetImage(AssetsManager.addProfile)
                                as ImageProvider,
                        child: userProfile?.profilePic == null
                            ? const Icon(
                                Icons.person,
                                size: 40,
                                color: Colors.grey,
                              )
                            : null,
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: AppColors.primaryBorderColor,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // User Name
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.person,
                        color: AppColors.primaryColor,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      CustomText(
                        text: userProfile?.name.isNotEmpty == true
                            ? userProfile!.name
                            : 'Mark Anderson',
                        size: 18,
                        weight: FontWeight.bold,
                        color: AppColors.primaryGreyColor,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Email
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.email,
                        color: AppColors.primaryColor,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      CustomText(
                        text: userProfile?.email ?? '<EMAIL>',
                        size: 14,
                        color: AppColors.textGreyColor,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Phone
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.phone,
                        color: AppColors.primaryColor,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      CustomText(
                        text: userProfile?.phone ?? '0123456789',
                        size: 14,
                        color: AppColors.textGreyColor,
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Current Plan Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                  color: const Color(0x1AE0E0E0),
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      spreadRadius: 1,
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                  border: Border.all(
                    color: const Color(0xFFE0E0E0),
                    width: 1,
                  )),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomText(
                    text: 'Your Current plan',
                    size: 16,
                    weight: FontWeight.w500,
                    color: AppColors.textGreyColor,
                  ),
                  const SizedBox(height: 16),

                  // Plan Details Container
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          spreadRadius: 1,
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Plan Details
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              text: userProfile?.currentPlan?.planName ??
                                  'Basic Plan',
                              size: 18,
                              weight: FontWeight.w600,
                              color: AppColors.primaryGreyColor,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                CustomText(
                                  text: userProfile?.currentPlan?.planAmount !=
                                          null
                                      ? '\$${userProfile!.currentPlan!.planAmount}'
                                      : '\$70',
                                  size: 18,
                                  weight: FontWeight.w600,
                                  color: Colors.red,
                                ),
                                CustomText(
                                  text: userProfile
                                              ?.currentPlan?.billingInterval !=
                                          null
                                      ? '/${userProfile!.currentPlan!.billingInterval.toLowerCase() == 'year' ? 'Yearly' : userProfile.currentPlan!.billingInterval}'
                                      : '/Yearly',
                                  size: 14,
                                  color: AppColors.textGreyColor,
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Upgrade Button
                        CustomButton(
                          text: 'Upgrade Plan',
                          onPressed: () {
                            // Navigate to upgrade plan
                          },
                          color: Colors.red,
                          textColor: Colors.white,
                          height: 48,
                          borderRadius: 8,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Billing Info
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.lightSecondaryBackgroundColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              text: userProfile?.currentPlan?.planAmount != null
                                  ? 'Billed ${userProfile!.currentPlan!.billingInterval.toLowerCase() == 'year' ? 'Yearly' : userProfile.currentPlan!.billingInterval} for \$${userProfile.currentPlan!.planAmount}'
                                  : 'Billed Yearly for \$70',
                              size: 14,
                              color: AppColors.textGreyColor,
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              text: 'Next payment Renew:',
                              size: 14,
                              color: AppColors.textGreyColor,
                            ),
                            CustomText(
                              text: userProfile?.currentPlan?.nextPaymentDate ??
                                  '12/10/2025',
                              size: 14,
                              weight: FontWeight.w500,
                              color: AppColors.primaryBorderColor,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
