// lib/core/utils/network_utils.dart
import 'dart:async';
import 'package:flutter/foundation.dart';

import 'exceptions/base_exception.dart';

Future<T?> callDataService<T>(
    Future<T> future, {
      Function(BaseException exception)? onError,
      Function(T response)? onSuccess,
      Function? onStart,
      Function? onComplete,
    }) async {
  Exception? excp;
  if (onStart != null) onStart();
  try {
    final T response = await future;
    debugPrint('callDataService - Success response received: $response');
    if (onSuccess != null) {
      debugPrint('callDataService - Calling onSuccess callback');
      onSuccess(response);
    }
    if (onComplete != null) onComplete();
    return response;
 // }
  // on ServiceUnavailableException catch (exception) {
  //   excp = exception;
  // } on UnauthorizedException catch (exception) {
  //   excp = exception;
  //   showErrorMessage(exception.message);
  // } on TimeoutException catch (exception) {
  //   excp = exception;
  //   showErrorMessage(exception.message ?? 'Timeout exception');
  // } on NetworkException catch (exception) {
  //   excp = exception;
  //   showErrorMessage(exception.message);
  // } on JsonFormatException catch (exception) {
  //   excp = exception;
  //   showErrorMessage(exception.message);
  // } on NotFoundException catch (exception) {
  //   excp = exception;
  //   showErrorMessage(exception.message);
  // } on ApiException catch (exception) {
  //   excp = exception;
  // } on AppException catch (exception) {
  //   excp = exception;
  //   showErrorMessage(exception.message);
  } catch (error) {
    excp = Exception("$error");
    debugPrint("LOG >>Controller>>>>>> $excp");
    debugPrint("LOG >> Controller >>>>>> $error");

    final baseException = error is BaseException
        ? error
        : AppException(message: error.toString()); // Use a concrete class here

    if (onError != null) {
      debugPrint('callDataService - Calling onError callback with: ${baseException.message}');
      onError(baseException);
    }
  }
  if (onError != null && excp is BaseException) {
    debugPrint('callDataService - Calling onError callback with: ${(excp as BaseException).message}');
    onError(excp);
  }
  if (onComplete != null) onComplete();
  return null;
}