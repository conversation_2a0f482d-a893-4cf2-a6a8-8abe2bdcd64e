import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../helpers/app_constant.dart';


final dioProvider = Provider<Dio>((ref) {
  final dio = Dio(
    BaseOptions(
      baseUrl: baseURL,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),

      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ),
  );

  // Add interceptors
  dio.interceptors.add(InterceptorsWrapper(
    onRequest: (options, handler) {
      if (kIsWeb) {
        options.headers['X-Requested-With'] = 'XMLHttpRequest';
      }
      return handler.next(options);
    },
    onError: (error, handler) {
      if (kDebugMode) {
        print('Dio Error: ${error.message}');
        print('URL: ${error.requestOptions.uri}');
        print('Response: ${error.response?.data}');
      }
      return handler.next(error);
    },
  ));

  if (kDebugMode) {
    dio.interceptors.add(LogInterceptor(
      responseBody: true,
      error: true,
      requestHeader: false,
    ));
  }


  dio.interceptors.add(LogInterceptor(  responseBody: true,
    error: true,
    requestHeader: true,)); // Optional
  return dio;
});
