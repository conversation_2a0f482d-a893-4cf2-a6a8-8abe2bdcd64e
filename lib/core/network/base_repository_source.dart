import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'dioprovider.dart';
import 'error_handlers.dart';
import 'exceptions/base_exception.dart';

abstract class BaseRepositorySource {
  Dio get dioClient => DioProvider.dioWithHeaderToken;

  Future<Response<T>> callApiWithErrorParser<T>(Future<Response<T>> api) async {
    try {
      Utils().hideKeyboardOnTap();
      Response<T> response = await api;
      if (response.statusCode != HttpStatus.ok ||
          (response.data as Map<String, dynamic>)['statusCode'] !=
              HttpStatus.ok) {
        debugPrint("Log >> statusCode is inValid >> ${response.statusCode}");
      }
      return response;
    } on DioException catch (dioError) {
      debugPrint("Log >> statusCode is inValid 111 >> ${dioError}");

      Exception exception = handleDioError(dioError);
      if (dioError.response?.statusCode == 401) {
        //  await Utils().logout();
      } else {
        debugPrint("LOG >>Throwing error from repository: >>>>>>> $exception : ${(exception as BaseException).message}");
        //SmartDialog.showToast(exception.message);
      }

      // Always throw the exception for error responses, don't return the response
      throw exception;

    } catch (error) {
      //  SmartDialog.dismiss();
      debugPrint("LOG >>Generic error: >>>>>>> $error");
      if (error is BaseException) {
        rethrow;
      }
      throw handleError("$error");
    }
  }
}
