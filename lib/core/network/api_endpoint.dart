class ApiEndpoints {
  static const String _authBase = '/auth';
  static const String _apiBase = '/api';

  // Auth endpoints
  static const String partner = '$_apiBase/auth/partner';
  static const String login = '$_apiBase/signin';
  static const String signUp = '$_authBase/register';
  static const String logout = '$_apiBase/logout';

  static const String verifyEmail = '$_authBase/verify-email';
  static const String verifyOtp = '$_authBase/verify-otp';
  static const String forgotPassword = '$_apiBase/forgot-password';
  static const String resetPassword = '$_apiBase/reset-password';
  static const String deleteAccount = '$_apiBase/delete-account';
  static const String plans = '$_apiBase/plans';

  // Other API endpoints
  static const String cookbooks = '$_apiBase/cookbooks';
  static String shoppingItems(int shoppingListId) => '$_apiBase/shopping-list/$shoppingListId/items';
  static String pantryItems(int pantryListId) => '$_apiBase/pantry/$pantryListId/items';
  static const String cookBook = '$_apiBase/cookbook';
  static String updateCookBook(String cookbookId) => '${cookBook}/$cookbookId';
  static const String recipes = '/recipes';

  static String getRecipes(String cookbookId) => '$cookBook/$cookbookId/recipes';
  static String getRecipesDetails(int cookbookId, int recipeId) => '$_apiBase/cookbook/$cookbookId/recipe/$recipeId';
  static String deletePantryItems(int pantryListId) => '$_apiBase/pantry/$pantryListId/items';
  static String deleteShoppingItems(int shoppingListId) => '$_apiBase/shopping-list/$shoppingListId/items/status';
  static const String createshoppinglist = '$_apiBase/shopping-list';
  static const String getshoppinglist = '$_apiBase/shopping-lists';

  static String categories= '$_apiBase/categories';

  static String cuisines='$_apiBase/cuisines';

  static String createRecipe(int cookbookId) => '$_apiBase/cookbook/$cookbookId/recipe';
  static String updateRecipe(int cookbookId,int recipeId)=> '$_apiBase/cookbook/$cookbookId/recipe/$recipeId';

// Add more endpoints as needed
  static String updateShopping(String shoppingId) => '$_apiBase/shopping-list/$shoppingId';
  static String deleteShoppingList(String shoppingId) => '$_apiBase/shopping-list/$shoppingId';
  static String updatePantry(String pantryId) => '$_apiBase/pantry/$pantryId';
  static String searchItems(String searchQuery) => '$_apiBase/items?search=$searchQuery';
  static const String userProfile = '$_apiBase/user/profile';
  static const String updateUserProfile = '$_apiBase/user/profile';
  static const String userTypes = '$_apiBase/user/types';

// recipe gpt Apis endpoint
  static String askAi='$_apiBase/ask/ai';
  static String askAiThreads='$_apiBase/ask/ai/threads';
  static String askAiMessages(int id) => '$_apiBase/ask/ai/thread/$id/messages';
  static String deleteThread='$_apiBase/delete/threads';
  static String syncListItem='$_apiBase/sync/items';

  static String fetchShoppingListsItems(int shoppingListId) => '$_apiBase/shopping-list/$shoppingListId/items';
  static String createPantry = '$_apiBase/pantry';
   static String pantryList = '$_apiBase/pantries';
   static String importCookBook = '$_apiBase/sync/cookbook';
  static String deletePantryList(String pantryId) => '$_apiBase/pantry/$pantryId';
  static String fetchPantryListsItems(int pantryId) => '$_apiBase/pantry/$pantryId/items';


  static String recipeClipper(String url) => '$_apiBase/parse/recipe';
  static String nutritions(int recipeId) => '$_apiBase/nutritions?recipeId=$recipeId';


  static String bannerApi = '$_apiBase/banners';


}