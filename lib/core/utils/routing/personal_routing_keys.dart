import '../../../../app/imports/core_imports.dart';

final shellNavigatorHome = GlobalKey<NavigatorState>(debugLabel: 'shellHome');
final shellNavigatorCookbook =
    GlobalKey<NavigatorState>(debugLabel: 'shellCookbook');
final shellNavigatorMeals = GlobalKey<NavigatorState>(debugLabel: 'shellMeals');
final shellNavigatorShopping =
    GlobalKey<NavigatorState>(debugLabel: 'shellShopping');
final shellNavigatorTips = GlobalKey<NavigatorState>(debugLabel: 'shellTips');
final shellNavigatorMedia = GlobalKey<NavigatorState>(debugLabel: 'shellMedia');
final shellNavigatorFav = GlobalKey<NavigatorState>(debugLabel: 'shellFav');
final shellNavigatorSync = GlobalKey<NavigatorState>(debugLabel: 'shellSync');
final shellNavigatorBackup =
    GlobalKey<NavigatorState>(debugLabel: 'shellBackup');
final shellNavigatorMyAccount = GlobalKey<NavigatorState>(debugLabel: 'shellMyAccount');
final shellNavigatorRecipeClipper = GlobalKey<NavigatorState>(debugLabel: 'reciepeClipper');
final shellNavigatorImageCropper =
    GlobalKey<NavigatorState>(debugLabel: 'shellImageCropper');
final shellNavigatorEditReceipe = GlobalKey<NavigatorState>(debugLabel: 'shellEditReceipe');
