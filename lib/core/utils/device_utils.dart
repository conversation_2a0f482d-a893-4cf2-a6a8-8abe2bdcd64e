import 'dart:io';
import 'dart:ui' as ui;

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
// import 'dart:html' as web;

import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class DeviceUtils {
  static bool isDesktop(BuildContext context) {
    if (kIsWeb) {
      final width = MediaQuery
          .of(context)
          .size
          .width;
      return width >= 1024; // Treat large web screens as desktop
    }
    return [
      TargetPlatform.macOS,
      TargetPlatform.windows,
      TargetPlatform.linux,
    ].contains(defaultTargetPlatform);
  }

  bool isTabletOrIpad(BuildContext context) {
    final double screenWidthPx = MediaQuery.of(context).size.width;
    return screenWidthPx >= 600 && screenWidthPx <= 1100;
  }

  /// Returns the device screen width in inches.
  double getScreenWidthInInches(BuildContext context) {
    // Get the logical screen width in pixels
    final double screenWidthPx = MediaQuery
        .of(context)
        .size
        .width;

    // Get the device pixel ratio (logical pixels per physical pixel)
    final double devicePixelRatio = MediaQuery
        .of(context)
        .devicePixelRatio;

    // Calculate physical pixels
    final double physicalWidthPx = screenWidthPx * devicePixelRatio;

    // Get pixels per inch (PPI)
    // Note: window.physicalSize.width gives physical pixels of the screen
    final double ppi = ui.window.physicalSize.width /
        ui.window.physicalSize.width * devicePixelRatio;

    // Calculate width in inches: physical pixels divided by PPI
    final double widthInInches = physicalWidthPx / ppi;

    return widthInInches;
  }

}
double getManualResponsiveFontSize(BuildContext context, double baseSize) {
  final screenWidth = MediaQuery.of(context).size.width;
  final isDesktop = DeviceUtils.isDesktop(context);
  final scaleFactor = (screenWidth / (isDesktop ? 1440 : 360)).clamp(0.8, 1.5);

  return baseSize * scaleFactor * (isDesktop ? 0.95 : 1.0); // No .sp
}

double responsiveFont(double baseSize) {
  double screenWidth = ScreenUtil().screenWidth;

  // You can fine-tune these thresholds
  if (screenWidth >= 1440) {
    return baseSize * 0.9; // Reduce slightly for big screens
  } else if (screenWidth <= 360) {
    return baseSize * 0.9;
  } else {
    return baseSize;
  }


}


DeviceType getDeviceType(BuildContext context) {
  final double width = MediaQuery.of(context).size.width;

  if (width >= 600 && width <= 1100) {
    return DeviceType.tablet;
  } else if (width < 600) {
    return DeviceType.mobile;
  } else {
    return DeviceType.web;
  }
}