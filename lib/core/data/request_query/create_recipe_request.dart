import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';

import '../../utils/Utils.dart';

Future<String?> convertFileToBase64(File? file) async {
  if (file == null) return null;
  final bytes = await file.readAsBytes();
  return base64Encode(bytes);
}

class CreateRecipeRequest {
  final String type;
  final String name;
  final String? description;
  final int categoryId;
  final int cuisineId;
  final int? coverRecipeMediaIndex;
  final int? existingAuthorMediaFileId;
  final String? yieldValue;
  final int? servings;
  final String? prepTime;
  final String? cookTime;
  final String? totalTime;
  final List<File>? recipeMediaFiles;
  final String? directionsJson;
  final List<File>? directionMediaFiles;
  final String? servingIdeas;
  final String? wine;
  final List<String>? ingredients;
  final List<int>? existingRecipeMediaFileIds;
  final String? authorName;
  final String? authorSource;
  final String? authorCopyright;
  final File? authorProfileFile;
  final String? notes;
  final String? AiImageUrl;
  final String? AiMessageId;

  CreateRecipeRequest({
    required this.type,
    required this.name,
    this.description,
    required this.categoryId,
    required this.cuisineId,
      this.coverRecipeMediaIndex,
    this.existingAuthorMediaFileId,
    this.yieldValue,
    this.servings,
    this.prepTime,
    this.cookTime,
    this.totalTime,
    this.recipeMediaFiles,
    this.directionsJson,
    this.directionMediaFiles,
    this.servingIdeas,
    this.existingRecipeMediaFileIds,
    this.wine,
    this.ingredients,
    this.authorName,
    this.authorSource,
    this.authorCopyright,
    this.authorProfileFile,
    this.notes,
    this.AiImageUrl,
    this.AiMessageId,
  });

  // Convert to FormData, excluding null values
  Future<FormData> toFormData() async {
    // Initialize map with only non-null values
    final map = <String, dynamic>{};
    if (type.isNotEmpty) map['Type'] = type;
    if (name.isNotEmpty) map['Name'] = name;
    if (description != null) map['description'] = description;
    map['CategoryId'] = categoryId; // Required field
    map['CuisineId'] = cuisineId; // Required field
    if (yieldValue != null) map['Yield'] = yieldValue;
    if (servings != null) map['Servings'] = servings;
    if (existingAuthorMediaFileId != null)
      map['ExistingAuthorMediaFileId'] = existingAuthorMediaFileId;
    if (prepTime != null) map['PrepTime'] = prepTime;
    if (cookTime != null) map['CookTime'] = cookTime;
    if (totalTime != null) map['TotalTime'] = totalTime;
    if (directionsJson != null) map['DirectionsJson'] = directionsJson;
    if (servingIdeas != null) map['ServingIdeas'] = servingIdeas;
    if (coverRecipeMediaIndex != null) map['CoverRecipeMediaIndex'] = coverRecipeMediaIndex;
    if (wine != null) map['Wine'] = wine;
    if (ingredients != null && ingredients!.isNotEmpty)
      map['Ingredients'] = ingredients;
    if (existingRecipeMediaFileIds != null &&
        existingRecipeMediaFileIds!.isNotEmpty)
      map['ExistingRecipeMediaFileIds'] = existingRecipeMediaFileIds;
    if (authorName != null) map['AuthorName'] = authorName;
    if (authorSource != null) map['AuthorSource'] = authorSource;
    if (authorCopyright != null) map['AuthorCopyright'] = authorCopyright;
    if (notes != null) map['Notes'] = notes;
    if (AiImageUrl != null) map['AiImageUrl'] = AiImageUrl;
    if (AiMessageId != null) map['AiMessageId'] = AiMessageId;

    // Create FormData from map
    final formData = FormData.fromMap(map);

    // Attach authorProfileFile if not null
    if (authorProfileFile != null) {
      formData.files.add(
        MapEntry(
          'authorProfileFile',
          await MultipartFile.fromFile(
            authorProfileFile!.path,
            filename: authorProfileFile!.path.split('/').last,
            contentType: MediaType(
                'image',
                authorProfileFile!.path
                    .split('.')
                    .last), // Adjust based on file type if needed
          ),
        ),
      );
    }

    // Attach recipeMediaFiles if not null and not empty
    if (recipeMediaFiles != null && recipeMediaFiles!.isNotEmpty) {
      for (var file in recipeMediaFiles!) {
        formData.files.add(MapEntry(
          'recipeMediaFiles',
          await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
            contentType: MediaType(
                Utils().isVideo(file.path) ? 'video' : 'image',
                file.path.split('.').last), // Dynamic content type
          ),
        ));
      }
    }

    // Attach directionMediaFiles if not null and not empty
    if (directionMediaFiles != null && directionMediaFiles!.isNotEmpty) {
      for (var file in directionMediaFiles!) {
        formData.files.add(MapEntry(
          'directionMediaFiles',
          await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
            contentType: MediaType(
                Utils().isVideo(file.path) ? 'video' : 'image',
                file.path.split('.').last), // Dynamic content type
          ),
        ));
      }
    }

    return formData;
  }

  // Convert to JSON, excluding null values
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (name.isNotEmpty) map['Name'] = name;
    if (description != null) map['description'] = description;
    map['CategoryId'] = categoryId; // Required field
    map['CuisineId'] = cuisineId; // Required field
    if (yieldValue != null) map['Yield'] = yieldValue;
    if (servings != null) map['Servings'] = servings;
    if (prepTime != null) map['PrepTime'] = prepTime;
    if (cookTime != null) map['CookTime'] = cookTime;
    if (totalTime != null) map['TotalTime'] = totalTime;
    if (directionsJson != null) map['DirectionsJson'] = directionsJson;
    if (servingIdeas != null) map['ServingIdeas'] = servingIdeas;
    if (wine != null) map['Wine'] = wine;
    if (ingredients != null && ingredients!.isNotEmpty)
      map['Ingredients'] = ingredients;
    if (authorName != null) map['AuthorName'] = authorName;
    if (authorSource != null) map['AuthorSource'] = authorSource;
    if (authorCopyright != null) map['AuthorCopyright'] = authorCopyright;
    if (notes != null) map['Notes'] = notes;
    if (AiMessageId != null) map['AiMessageId'] = AiMessageId;
    if (AiImageUrl != null) map['AiImageUrl'] = AiImageUrl;
    if (coverRecipeMediaIndex != null) map['CoverRecipeMediaIndex'] = coverRecipeMediaIndex;
    if (authorProfileFile != null)
      map['authorProfileFile'] = authorProfileFile!.path;
    if (recipeMediaFiles != null && recipeMediaFiles!.isNotEmpty) {
      map['recipeMediaFiles'] =
          recipeMediaFiles!.map((file) => file.path).toList();
    }
    if (directionMediaFiles != null && directionMediaFiles!.isNotEmpty) {
      map['directionMediaFiles'] =
          directionMediaFiles!.map((file) => file.path).toList();
    }
    return map;
  }

  // Pretty-print JSON with each field on a new line
  String toJsonString() {
    const encoder =
        JsonEncoder.withIndent('  '); // Use 2 spaces for indentation
    return encoder.convert(toJson());
  }
}
