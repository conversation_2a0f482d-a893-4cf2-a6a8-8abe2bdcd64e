import 'package:flutter/widgets.dart';
import 'package:flutter/material.dart';
import '../../presentation/cookbook/cookbook_details_screen.dart';
import '../data/models/cookbook.dart';

class CookbookRouterDelegate extends RouterDelegate<RouteSettings> with ChangeNotifier, PopNavigatorRouterDelegateMixin<RouteSettings> {
  final GlobalKey<NavigatorState> navigatorKey;
  Cookbook? selectedCookbook;

  CookbookRouterDelegate() : navigatorKey = GlobalKey<NavigatorState>();

  @override
  Widget build(BuildContext context) {
    return Navigator(
      key: navigatorKey,
      onPopPage: (route, result) {
        if (!route.didPop(result)) {
          return false;
        }
        selectedCookbook = null;
        notifyListeners();
        return true;
      },
      pages: [
        MaterialPage(
          key: const ValueKey('CookbookList'),
          child: Container(), // Replace with your cookbook list screen
        ),
        if (selectedCookbook != null)
          MaterialPage(
            key: ValueKey(selectedCookbook!.id),
            child: CookbookDetailScreen(), // Pass the cookbook here
            arguments: selectedCookbook,
          ),
      ],
    );
  }

  @override
  Future<void> setNewRoutePath(RouteSettings configuration) async {
    // This is called when the app is launched with a deep link
    // Not needed for this example
  }
}
