import 'dart:convert';
import 'package:flutter/widgets.dart';

import '../data/models/cookbook.dart';

class CookbookRouteInformationParser extends RouteInformationParser<RouteSettings> {
  @override
  Future<RouteSettings> parseRouteInformation(RouteInformation routeInformation) async {
    final uri = Uri.parse(routeInformation.uri.toString());
    if (uri.pathSegments.length == 2 && uri.pathSegments.first == 'cookbook' && uri.pathSegments[1] == 'cookbookDetail' && routeInformation.state != null) {
      try {
        final cookbook = Cookbook.fromJson(jsonDecode(routeInformation.state as String));
        return RouteSettings(name: '/cookbook/cookbookDetail', arguments: cookbook);
      } catch (e) {
        print('Failed to parse Cookbook from state: $e');
        return const RouteSettings(name: '/'); // Or some error route
      }
    }
    return RouteSettings(name: routeInformation.uri.toString());
  }

  @override
  RouteInformation restoreRouteInformation(RouteSettings configuration) {
    return RouteInformation(uri: Uri.parse(configuration.name!));
  }
}
