import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class InAppYouTubePlayer extends StatefulWidget {
  final String videoId;

  const InAppYouTubePlayer({Key? key, required this.videoId}) : super(key: key);

  @override
  State<InAppYouTubePlayer> createState() => _InAppYouTubePlayerState();
}

class _InAppYouTubePlayerState extends State<InAppYouTubePlayer> {
  InAppWebViewController? webViewController;

  final String baseEmbedUrl = "https://www.youtube.com/embed/";

  @override
  Widget build(BuildContext context) {
    final String embedUrl = "$baseEmbedUrl${widget.videoId}?autoplay=0&playsinline=1";
    return Container(
      height: 350.h,
      width: 600.w,
      child: InAppWebView(
        initialUrlRequest: URLRequest(
          url: WebUri(embedUrl), // ✅ FIXED
        ),
        initialSettings: InAppWebViewSettings(
          mediaPlaybackRequiresUserGesture: false,
          allowsInlineMediaPlayback: true,
          javaScriptEnabled: true,
          useHybridComposition: true,
          iframeAllowFullscreen: true,

          // 🟢 Add these lines:
          disableContextMenu: true,
          javaScriptCanOpenWindowsAutomatically: false,
          // androidOverScrollMode: AndroidOverScrollMode.NEVER,
          // androidUseWideViewPort: true,
          supportZoom: false,
          builtInZoomControls: false,
          displayZoomControls: false,

        ),
        onWebViewCreated: (controller) {
          webViewController = controller;
        },
      ),
    );
  }
}