import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import '../utils/device_utils.dart';
import 'custom_button.dart';
import 'custom_text.dart';

class CommonConfirmDialog extends StatelessWidget {
  final String title;
  final String subtitle;
  final String confirmText;
  final String cancelText;
  final Color? confirmTextColor;

  const CommonConfirmDialog({
    Key? key,
    required this.title,
    required this.subtitle,
    this.confirmText = 'Delete',
    this.cancelText = 'Cancel',
    this.confirmTextColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Center(
        child:
        CustomText(
          text:  title,
          color: AppColors.blackTextColor,
          size: DeviceUtils().isTabletOrIpad(context) ? 24 : 24,
          weight: FontWeight.w700,
          align:TextAlign.center ,
        ),

      ),
      content:
      CustomText(
        text:  subtitle,
        color: AppColors.blackTextColor,
        size: DeviceUtils().isTabletOrIpad(context) ? 14 : 14,
        weight: FontWeight.w400,
        align:TextAlign.center ,
      ),

      actionsPadding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 30),
      actionsAlignment: MainAxisAlignment.center,
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, false),
          child:
          Text(
            cancelText,
            style: context.theme.textTheme.labelLarge!.copyWith(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).primaryColor, // Adjust as needed
            ),
          ),
        ),
        SizedBox(width: 56), // space between buttons
        CustomButton(
          width: 170,
          fontSize: 14,
          text: confirmText,
          onPressed: () => Navigator.pop(context, true),
        ),
      ],
    );
  }
}

