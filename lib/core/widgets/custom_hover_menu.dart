import 'package:flutter/material.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

class CustomHoverMenu extends StatefulWidget {
  final List<String> items;
  final List<IconData> itemIcons;
  final Function(String) onItemSelected;
  final double menuWidth;
  final double iconSize;
  final Color iconColor;
  final IconData triggerIcon;
  final String? menuTitle;
  final String? selectedItem;

  const CustomHoverMenu({
    super.key,
    required this.items,
    required this.itemIcons,
    required this.onItemSelected,
    this.menuWidth = 250.0,
    this.iconSize = 28.0,
    this.iconColor = Colors.black54,
    this.triggerIcon = Icons.more_vert,
    this.menuTitle,
    this.selectedItem,
  }) : assert(items.length == itemIcons.length, 'Items and itemIcons must have the same length');

  @override
  State<CustomHoverMenu> createState() => _CustomHoverMenuState();
}

class _CustomHoverMenuState extends State<CustomHoverMenu> {
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();
  bool _isMenuOpen = false;

  void _toggleMenu() {
    if (_isMenuOpen) {
      _overlayEntry?.remove();
      _overlayEntry = null;
      _isMenuOpen = false;
    } else {
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
      _isMenuOpen = true;
    }
    setState(() {});
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: _toggleMenu, // Close menu on outside tap
        child: Stack(
          children: [
            Positioned(
              left: offset.dx - (widget.menuWidth - size.width),
              top: offset.dy + size.height + 8,
              width: widget.menuWidth,
              child: Material(
                color: Colors.transparent,
                child: GestureDetector(
                  onTap: () {}, // Prevent taps on menu from closing it
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black26,
                          blurRadius: 8,
                          offset: Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: widget.items
                          .asMap()
                          .entries
                          .map((entry) => _menuItem(
                        entry.value,
                        widget.itemIcons[entry.key],
                        entry.key == widget.items.length - 1,
                      ))
                          .toList(),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _menuItem(String title, IconData icon, bool isLast) {
    bool isSelected = title == widget.selectedItem;

    return StatefulBuilder(
      builder: (context, setState) {
        bool isHovered = false;

        return MouseRegion(
          cursor: SystemMouseCursors.click,
          onEnter: (_) => setState(() => isHovered = true),
          onExit: (_) => setState(() => isHovered = false),
          child: InkWell(
            onTap: () {
              widget.onItemSelected(title);
              _toggleMenu();
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                border: Border(
                  bottom: isLast ? BorderSide.none : BorderSide(color: Colors.grey[200]!),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    icon,
                    size: 20,
                    color: isSelected || isHovered ? Colors.red : Colors.black54,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        color: isSelected || isHovered ? Colors.red : Colors.black,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: Tooltip(
          message: widget.menuTitle ?? '',
          // Uncomment and customize if needed
          // textStyle: context.theme.textTheme.displaySmall!.copyWith(
          //   color: AppColors.primaryLightTextColor,
          //   fontWeight: FontWeight.w400,
          //   fontSize: headingThreeFontSize * MediaQuery.textScaleFactorOf(context),
          // ),
          child: GestureDetector(
            onTap: _toggleMenu,
            child: Icon(
              widget.triggerIcon,
              size: widget.iconSize,
              color: widget.iconColor,
            ),
          ),
        ),
      ),
    );
  }
}