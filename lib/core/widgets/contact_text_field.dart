import 'package:country_pickers/country.dart';
import 'package:country_pickers/country_pickers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import '../../app/theme/colors.dart';
import '../utils/device_utils.dart';
import '../utils/validator.dart';
import 'custom_input_field.dart';
import 'custom_text.dart';

class ContactTextField extends StatefulWidget {
  final TextEditingController controller;
  final Country selectedCountry;
  final void Function(Country) onCountryChanged;
  final String? Function(String?)? validator;
  final bool isUpdateMode;
  final VoidCallback? onClear;

  const ContactTextField({
    Key? key,
    required this.controller,
    required this.selectedCountry,
    required this.onCountryChanged,
    this.validator,
    this.isUpdateMode = false,
    this.onClear,
  }) : super(key: key);

  @override
  _ContactTextFieldState createState() => _ContactTextFieldState();
}

class _ContactTextFieldState extends State<ContactTextField> {
  void _openCountryPickerDialog() {
    final isTablet = DeviceUtils().isTabletOrIpad(context);

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: SizedBox(
          width: MediaQuery.of(context).size.width * (isTablet ? 0.4 : 0.26),
          height: MediaQuery.of(context).size.height * (isTablet ? 0.6 : 0.4),
          child: Theme(
            data: Theme.of(context).copyWith(
              primaryColor: AppColors.primaryColor,
              textTheme: Theme.of(context).textTheme.copyWith(
                    bodyLarge: const TextStyle(color: Colors.black),
                    bodyMedium: const TextStyle(color: Colors.black),
                    bodySmall: const TextStyle(color: Colors.black),
                    titleMedium: const TextStyle(color: Colors.black),
                    titleSmall: const TextStyle(color: Colors.black),
                  ),
            ),
            child: CountryPickerDialog(
              titlePadding: const EdgeInsets.all(16.0),
              searchCursorColor: AppColors.primaryColor,
              searchInputDecoration: const InputDecoration(
                hintText: 'Search...',
                hintStyle: TextStyle(color: Colors.grey),
              ),
              isSearchable: true,
              title: CustomText(
                text: 'Select your phone code',
                size: 20,
                weight: FontWeight.w600,
                color: AppColors.primaryLightTextColor,
              ),
              onValuePicked: (Country country) =>
                  widget.onCountryChanged(country),
              priorityList: [
                CountryPickerUtils.getCountryByIsoCode('US'),
                CountryPickerUtils.getCountryByIsoCode('CA'),
                CountryPickerUtils.getCountryByIsoCode('GB'),
                CountryPickerUtils.getCountryByIsoCode('AU'),
                CountryPickerUtils.getCountryByIsoCode('IN'),
              ],
              itemBuilder: _buildDialogItem,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDialogItem(Country country) {
    final isTablet = DeviceUtils().isTabletOrIpad(context);

    return Container(
      padding: EdgeInsets.symmetric(
          horizontal:   8, vertical:   12),
      child: Row(
        children: <Widget>[
          CountryPickerUtils.getDefaultFlagImage(country),
          SizedBox(width:  8.0),
          CustomText(
            text: "+${country.phoneCode}",
            size: 16,
            weight: FontWeight.w500,
            color: AppColors.primaryLightTextColor,
          ),
          SizedBox(width:  8.0),
          Flexible(
            child: CustomText(
              text: country.name,
              size: 16,
              weight: FontWeight.w500,
              color: AppColors.primaryLightTextColor,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils().isTabletOrIpad(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      // Align items to top for validation
      children: [
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: _openCountryPickerDialog,
            child: Container(
              height: isTablet ? 48: 40, // Matches CustomInputField height
              // width: isTablet ? 120 : 100,
              padding: EdgeInsets.symmetric(horizontal:  8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.15),
                    spreadRadius: 2,
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CountryPickerUtils.getDefaultFlagImage(
                      widget.selectedCountry),
                  SizedBox(width:  4),
                  Text(
                    '+${widget.selectedCountry.phoneCode}',
                    style: TextStyle(
                      fontSize:   14,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(width:   6),
                  SvgPicture.asset(
                    AssetsManager.droparrow,
                    height:  16,
                    width:   16,
                  ),
                ],
              ),
            ),
          ),
        ),
        SizedBox(width:  8),
        Expanded(
          child: CustomInputField(
            hintText: 'Phone',
            controller: widget.controller,
            validator: widget.validator ?? Validator.validatePhoneNumber,
            isUpdateMode: widget.isUpdateMode,
            keyboardType: TextInputType.phone,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            maxLength: 15,
            onClear: widget.onClear,
          ),
        ),
      ],
    );
  }
}
