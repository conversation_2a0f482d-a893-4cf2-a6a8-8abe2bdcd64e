import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mastercookai/app/assets_manager.dart';

class CommonImage extends StatelessWidget {
  final dynamic imageSource; // String path (URL or asset) or File
  final double? height;
  final double? width;
  final BoxFit fit;
  final String? placeholder; // Optional placeholder asset
  final double? borderRadius; // Optional border radius for rounded corners

  const CommonImage({
    super.key,
    required this.imageSource,
    this.height,
    this.width,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.borderRadius,
  });

  bool get _isNetwork => imageSource is String && imageSource.toString().startsWith('http');
  bool get _isAsset => imageSource is String && !imageSource.toString().startsWith('http');
  bool get _isFile => imageSource is File || (imageSource is String && !kIsWeb && File(imageSource).existsSync());

  @override
  Widget build(BuildContext context) {
    Widget imageWidget;
    try {
      if (_isNetwork) {
        imageWidget = Image.network(
          imageSource,
          height: height,
          width: width,
          fit: fit,
          errorBuilder: (_, __, ___) => _errorImage(),
        );
      } else if (_isFile) {
        imageWidget = Image.file(
          imageSource is File ? imageSource : File(imageSource),
          height: height,
          width: width,
          fit: fit,
          errorBuilder: (_, __, ___) => _errorImage(),
        );
      } else if (_isAsset) {
        imageWidget = Image.asset(
          imageSource,
          height: height,
          width: width,
          fit: fit,
          errorBuilder: (_, __, ___) => _errorImage(),
        );
      } else {
        imageWidget = _errorImage();
      }
    } catch (e) {
      imageWidget = _errorImage();
    }

    // Apply borderRadius if provided
    return borderRadius != null
        ? ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius!),
      child: imageWidget,
    )
        : imageWidget;
  }

  Widget _errorImage() {
    final placeholderImage = placeholder ?? AssetsManager.cb_place_holder;
    return Image.asset(
      placeholderImage,
      height: height,
      width: width,
      fit: fit,
    );
  }
}