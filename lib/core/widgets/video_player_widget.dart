import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import 'package:video_player/video_player.dart';
import '../../app/imports/core_imports.dart';

class VideoPlayerWidget extends StatefulWidget {
  final File? mediaFile;
  final String? mediaUrl;
  final String? thumbnailPath;
  final double height;
  final Map<String, String>? headers;

  const VideoPlayerWidget({
    super.key,
    this.mediaFile,
    this.mediaUrl,
    this.thumbnailPath,
    this.headers,
    this.height = 200.0,
  });

  @override
  _VideoPlayerWidgetState createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  VideoPlayerController? _controller;
  bool _hasError = false;
  String? _errorMessage;
  bool _isPlaying = false;
  Duration _lastPosition = Duration.zero;


  @override
  void initState() {
    super.initState();
     _initVideo();
  }

  Future<void> _initVideo() async {
    try {
      // Skip initialization on iOS Simulator
      if (Platform.isIOS &&
          !kIsWeb &&
          Platform.environment.containsKey('SIMULATOR_DEVICE_NAME')) {
        setState(() {
          _hasError = true;
          _errorMessage = "Video playback not supported on iOS Simulator";
        });
        return;
      }

      if (widget.mediaFile != null) {
        _controller = VideoPlayerController.file(widget.mediaFile!);
        print("Initializing video from file: ${widget.mediaFile!.path}");
      } else if (widget.mediaUrl != null && widget.mediaUrl!.isNotEmpty) {
        _controller = VideoPlayerController.networkUrl(
          Uri.parse(widget.mediaUrl!),
       //   httpHeaders: widget.headers ?? {},
          videoPlayerOptions: VideoPlayerOptions(
            mixWithOthers: true,
            allowBackgroundPlayback: false,
          ),
        );
        print("Initializing video from URL: ${widget.mediaUrl}");
      } else {
        setState(() {
          _hasError = true;
          _errorMessage = "No valid media source provided";
        });
        return;
      }

      await _controller!.initialize();
      // Listen to controller state changes
      _controller!.addListener(_onControllerUpdate);
      // Attempt to play the video briefly to test playback
      await _controller!.play();
      await Future.delayed(const Duration(milliseconds: 100));
      await _controller!.pause();
      setState(() {});
    } catch (e, stackTrace) {
      debugPrint("Video initialization failed: $e\nStackTrace: $stackTrace");
      setState(() {
        _hasError = true;
        _errorMessage = e.toString().contains("401")
            ? "Unauthorized access to video. Please check authentication."
            : "Failed to load video: $e";
      });
    }
  }

  void _onControllerUpdate() {
    if (!mounted) return;
    final controller = _controller;
    if (controller == null || !controller.value.isInitialized) return;

    final newPlayingState = controller.value.isPlaying;
    final newPosition = controller.value.position;
    final isBuffering = controller.value.isBuffering;

    // Update only if playing state, position, or buffering state changes
    if (newPlayingState != _isPlaying ||
        newPosition.inSeconds != _lastPosition.inSeconds ||
        controller.value.isBuffering) {
      setState(() {
        _isPlaying = newPlayingState;
        _lastPosition = newPosition;
      });
      print(
          "Video position: ${_formatDuration(newPosition)}, isPlaying: $_isPlaying, isBuffering: $isBuffering");
    }
  }

  @override
  void dispose() {
    _controller?.removeListener(_onControllerUpdate);
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError || _controller == null || !_controller!.value.isInitialized) {
      return SizedBox(
        height: 120.h,
        child: CommonImage(
          imageSource: widget.thumbnailPath ?? '',
          placeholder: AssetsManager.recipe_place_holder,
        ),
      );
    }

    return AspectRatio(
      aspectRatio: _controller!.value.aspectRatio,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            height: widget.height,
            child: VideoPlayer(_controller!),
          ),
          // Buffer loader
          if (_controller!.value.isBuffering)
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          // Play/Pause button
          Positioned(
            bottom: 40.h,
            child: IconButton(
              icon: Icon(
                _isPlaying ? Icons.pause : Icons.play_arrow,
                color: Colors.white,
                size: 40,
              ),
              onPressed: () {
                if (_controller!.value.isPlaying) {
                  _controller!.pause();
                } else {
                  _controller!.play();
                }
                print(
                    "Video ${_controller!.value.isPlaying ? 'paused' : 'playing'}");
              },
            ),
          ),
          // Seek bar
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: VideoProgressIndicator(
              _controller!,
              allowScrubbing: true,
              colors: const VideoProgressColors(
                playedColor: Colors.blue,
                bufferedColor: Colors.blueGrey,
                backgroundColor: Colors.grey,
              ),
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
            ),
          ),
          // Video position and duration
          Positioned(
            bottom: 20.h,
            left: 8.w,
            child: Text(
              _formatDuration(_controller!.value.position),
              style: TextStyle(color: Colors.white, fontSize: 12.sp),
            ),
          ),
          Positioned(
            bottom: 20.h,
            right: 8.w,
            child: Text(
              _formatDuration(_controller!.value.duration),
              style: TextStyle(color: Colors.white, fontSize: 12.sp),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes.toString().padLeft(2, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }
}