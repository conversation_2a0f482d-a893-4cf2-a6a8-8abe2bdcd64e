import 'dart:io';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'dotted_circle_painter.dart';

class CircularImage extends StatelessWidget {
  final File? imageFile; // Image from File (e.g., local storage)
  final String? imageUrl; // Image from network URL
  final String placeholderAsset; // Placeholder image asset path
  final double radius; // Size of the circular image
  final VoidCallback? onTap; // Callback for tap gesture
  final Color borderColor; // Color for the dotted border
  final String placeholderText; // Text to show when no image is provided

  const CircularImage({
    super.key,
    this.imageFile,
    this.imageUrl,
    this.placeholderAsset =
        'assets/images/placeholder.png', // Default placeholder
    this.radius = 150.0, // Default radius
    this.onTap,
    this.borderColor = Colors.grey,
    this.placeholderText = 'Add Image',
  });

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils().isTabletOrIpad(context);

    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          // If imageFile or imageUrl is provided, show the image; otherwise, show placeholder
          imageFile != null || (imageUrl != null && imageUrl!.isNotEmpty)
              ? CircleAvatar(
                  radius: isTablet ? 70 : radius.w,
                  backgroundImage: imageFile != null
                      ? FileImage(imageFile!)
                      : NetworkImage(imageUrl!) as ImageProvider,
                  onBackgroundImageError: imageUrl != null
                      ? (exception, stackTrace) => AssetImage(placeholderAsset)
                      : null,
                )
              : CustomPaint(
                  painter: DottedCirclePainter(),
                  child: CircleAvatar(
                    radius: isTablet ? 70 : radius.w,
                    backgroundColor: Colors.white,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(color: Colors.grey.shade300),
                        color: Colors.white,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5.0),
                        child: TextButton(
                          onPressed: onTap,
                          child: Text(
                            placeholderText,
                            style: context.textTheme.titleMedium?.copyWith(
                              color: const Color(
                                  0xFF333333), // Replace with AppColors.primaryLightTextColor
                              fontSize: 10,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
        ],
      ),
    );
  }
}
