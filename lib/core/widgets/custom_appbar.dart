import 'package:mastercookai/core/utils/device_utils.dart';

import '../../app/imports/core_imports.dart';
import '../../app/imports/packages_imports.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final VoidCallback? onPressed;
  final bool showDrawerIcon;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.onPressed,
    this.showDrawerIcon = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      centerTitle: false,
      elevation: 2,
      titleSpacing: 0,
      automaticallyImplyLeading: !kIsWeb,
      leading: kIsWeb
          ? null
          : IconButton(
              icon: const Icon(Icons.arrow_back_ios_sharp),
              onPressed: onPressed ??
                  () {
                    if (Navigator.of(context).canPop()) {
                      Navigator.of(context).pop();
                    } else {
                      // You can fallback to GoRouter navigation if needed
                      context.go(Routes.home);
                    }
                  },
            ),
      // title: Padding(
      //   padding: EdgeInsets.only(left: kIsWeb ? 16.0 : 0.0),
      //   child: Row(
      //     children: [
      //       Expanded(
      //         child: Text(
      //           title,
      //           style: context.theme.textTheme.bodyMedium!.copyWith(
      //             color: AppColors.texGreyColor,
      //             fontWeight: FontWeight.w500,
      //             fontFamily: 'Inter',
      //             fontSize: 16,//30.sp,
      //           ),
      //           overflow: TextOverflow.ellipsis,
      //         ),
      //       ),
      //       // Add list icon for small screens after the title
      //       if (showDrawerIcon && !kIsWeb)
      //         Builder(
      //           builder: (context) => IconButton(
      //             icon: const Icon(Icons.list),
      //             onPressed: () => Scaffold.of(context).openDrawer(),
      //           ),
      //         ),
      //     ],
      //   ),
      // ),
      title: SizedBox(
        width: double.infinity, // Ensures it takes available width
        child: Padding(
          padding: EdgeInsets.only(left: kIsWeb ? 16.0 : 0.0),
          child: Row(
            children: [
              Flexible( // Instead of Expanded (allows text to shrink)
                child: Text(
                  title,
                  style: context.theme.textTheme.bodyMedium!.copyWith(
                    color: AppColors.texGreyColor,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Inter',
                    fontSize: 16,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (showDrawerIcon && !kIsWeb)
                 Padding(
                  padding: const EdgeInsets.only(left: 4.0), // Adjust as needed
                  child: Builder(
                    builder: (context) => IconButton(
                     // icon: const Icon(Icons.list),
                       icon: Container(
                        margin: EdgeInsets.symmetric(horizontal: 5.w),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.black38.withOpacity(.2)),
                          color: AppColors.primaryColor,
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: Icon(
                          Icons.keyboard_arrow_down,
                          size: DeviceUtils().isTabletOrIpad(context) ? 14 : 20.w,
                          color: AppColors.secondaryColor,
                        ),
                  ),
                      onPressed: () => Scaffold.of(context).openDrawer(),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
      actions: actions ?? [

      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
