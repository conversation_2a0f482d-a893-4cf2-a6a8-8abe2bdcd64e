import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

import '../../app/imports/core_imports.dart';
import '../widgets/uploading_dialog.dart';

String baseURL = '';
String bearerToken = '';

showLoader() {
  SmartDialog.show(builder: (context) {
    return Center(
      child: LoadingAnimationWidget.fallingDot(
        color: Colors.white,
        size: 50.0,
      ),
    );
  });
}



void showLeftLoader(String fileName, String cookBookName) {
  //final syncNotifier = context.read<syncNotifierProvider.notifier>();
  SmartDialog.show(
    backDismiss: false, // Prevent dismissal by tapping outside
    clickMaskDismiss: false, // Prevent dismissal by clicking mask
    builder: (_) {
      return Align(
        alignment: Alignment.topRight,
        child: Material(
          color: Colors.transparent,
          child: UploadingDialog(
            title:   cookBookName.isEmpty?"Cookbook list is uploading...":'Recipes list is uploading...',
            subtitle: cookBookName.isEmpty? fileName:cookBookName,
            progress:100, // Use dynamic progress from SyncNotifier
          ),
        ),
      );
    },
  );
}

hideLoader() {
  SmartDialog.dismiss();
}
