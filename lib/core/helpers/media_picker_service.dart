import 'dart:io';
import 'package:file_picker/file_picker.dart';

class MediaPickerService {
  static Future<List<File>> pickImages({
    bool allowMultiple = true,
    bool allowVideo = false,
    bool videoOnly = false, // New parameter to enforce video-only selection
  }) async {
    final fileType = videoOnly ? FileType.custom : (allowVideo ? FileType.media : FileType.image);
    final allowedExtensions = videoOnly ? ['mp4', 'mov'] : null; // Restrict to videos for videoOnly
    final result = await FilePicker.platform.pickFiles(
      type: fileType,
      allowMultiple: allowMultiple,
      allowedExtensions: allowedExtensions,
    );

    if (result != null && result.files.isNotEmpty) {
      return result.paths.map((path) => File(path!)).toList();
    }
    return [];
  }

  static Future<File?> pickSingleImage({bool allowVideo = false, bool videoOnly = false}) async {
    try {
      final fileType = videoOnly ? FileType.custom : (allowVideo ? FileType.media : FileType.image);
      final allowedExtensions = videoOnly ? ['mp4', 'mov'] : null;
      final result = await FilePicker.platform.pickFiles(
        type: fileType,
        allowMultiple: false,
        allowedExtensions: allowedExtensions,
      );
      if (result != null && result.files.single.path != null) {
        return File(result.files.single.path!);
      }
    } catch (e, stack) {
      print('File picker error: $e\n$stack');
    }
    return null;
  }
}