
import 'package:shared_preferences/shared_preferences.dart';

import '../../app/imports/packages_imports.dart';
import '../data/models/login_model.dart';


final localStorageProvider = Provider<LocalStorageService>((ref) {
  return LocalStorageService();
});


class LocalStorageService {
  static const _tokenKey = 'token';
  static const _usernameKey = 'username';
  static const _passwordKey = 'password';


  Future<void> saveLoginData({
    required LoginModel loginModel,
    required String email,
    required String password,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('token', loginModel.data!.token ??  '');
    await prefs.setString('email', email);
    await prefs.setString('password', password);
  }

  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  Future<Map<String, String?>> getLoginCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'username': prefs.getString(_usernameKey),
      'password': prefs.getString(_passwordKey),
    };
  }

  Future<void> clearLoginData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_usernameKey);
    await prefs.remove(_passwordKey);
  }

  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');
    return token != null && token.isNotEmpty;
  }

  Future<void> saveApiKey(String apiKey) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('apiKey', apiKey);
  }

  Future<String?> getApiKey() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('apiKey');
  }
  
}
