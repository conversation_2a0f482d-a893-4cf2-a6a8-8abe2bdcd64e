import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'dart:io' show Platform, NetworkInterface, InternetAddressType;

class DeviceInfoService {
  static final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();

  static Future<String> getDeviceName() async {
    if (kIsWeb) {
      WebBrowserInfo webBrowserInfo = await _deviceInfoPlugin.webBrowserInfo;
      print('Running on ${webBrowserInfo.userAgent}');
      return webBrowserInfo.browserName.name ?? "Unknown Web Device";
    }

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await _deviceInfoPlugin.androidInfo;
      return androidInfo.model ?? "Unknown Android Device";
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await _deviceInfoPlugin.iosInfo;
      return iosInfo.utsname.machine ?? "Unknown iOS Device";
    }
    return "Unknown Device";
  }

  static Future<String> getDeviceId() async {
    if (kIsWeb) {
      WebBrowserInfo webBrowserInfo = await _deviceInfoPlugin.webBrowserInfo;
      print('Running on ${webBrowserInfo.userAgent}');
      return webBrowserInfo.browserName.name ?? "Unknown Web id";
    }


    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await _deviceInfoPlugin.androidInfo;
      return androidInfo.id ?? "unknown_android_id";
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await _deviceInfoPlugin.iosInfo;
      return iosInfo.identifierForVendor ?? "unknown_ios_id";
    }
    return "unknown_device_id";
  }

  static Future<String> getIpAddress() async {
    if (kIsWeb) return "web_ip_unknown";

    try {
      for (var interface in await NetworkInterface.list(
        includeLinkLocal: true,
        type: InternetAddressType.IPv4,
      )) {
        for (var addr in interface.addresses) {
          if (!addr.isLoopback) {
            return addr.address;
          }
        }
      }
    } catch (e) {
      return 'Failed to get IP: $e';
    }

    return 'IP not found';
  }

  static Future<Map<String, String>> getDeviceInfo() async {
    final name = await getDeviceName();
    final id = await getDeviceId();
    return {
      "device_name": name,
      "device_id": id,
    };
  }
}
