import 'package:flutter/material.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart'
    hide NutritionInfo;
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import '../../../app/imports/packages_imports.dart';
import '../data/models/category_response.dart';
import '../data/models/cuisines_response.dart';
import '../data/models/nutritionInfo_response.dart';
import '../data/models/recipe_response.dart';
import '../network/base_notifier.dart';
import '../network/network_utils.dart';

// Provider for SingleRecipeNotifier
final nutritionInfoNotifierProvider =
    StateNotifierProvider<NutrionsRecipeNotifier, AppState<NutritionInfoData>>(
  (ref) => NutrionsRecipeNotifier(ref),
);

class NutrionsRecipeNotifier extends BaseNotifier<NutritionInfoData> {
  NutrionsRecipeNotifier(Ref ref)
      : super(ref, const AppState<NutritionInfoData>());

  Future<void> getNutritions({
    required BuildContext context,
    required int recipeId,
  }) async {
    callDataService(
      repo.getNutritions(recipeId: recipeId),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (NutritionInfoResponse response) {
        if (response.status == 200) {
          if (response.data?.nutritionInfo != null) {
             state = state.copyWith(status: AppStatus.success, data: response.data?.nutritionInfo);
          } else {
             state = state.copyWith(status: AppStatus.error, data: null);
          }
        } else {
          state = state.copyWith(status: AppStatus.error, data: null);
        }
      },
      onError: (e) {
        state = state.copyWith(status: AppStatus.error);
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
        print('Recipe fetch error: $e');
      },
      onComplete: () => print('Recipe fetch completed'),
    );
  }
}
