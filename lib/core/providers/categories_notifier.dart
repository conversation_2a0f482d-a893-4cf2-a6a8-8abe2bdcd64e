import 'package:flutter/material.dart';
import 'package:mastercookai/core/data/models/category_response.dart';
 import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import '../../../../app/imports/packages_imports.dart';
import '../data/models/category_model.dart';
import '../network/base_notifier.dart';
import '../network/network_utils.dart';


// Provider for CategoriesNotifier
final categoriesNotifierProvider = StateNotifierProvider<CategoriesNotifier, AppState<List<Categories>>>(
      (ref) => CategoriesNotifier(ref),
);

class CategoriesNotifier extends BaseNotifier<List<Categories>> {
  CategoriesNotifier(Ref ref) : super(ref, const AppState<List<Categories>>());

  Future<void> fetchCategories({
    required BuildContext context,
    bool reset = false,
    int pageSize = 20,
  }) async {
    if (!reset && (state.hasMore == false || state.status == AppStatus.loadingMore)) {
      return;
    }

    final currentPage = reset ? 1 : state.currentPage + 1;

    callDataService(
      repo.fetchCategories(),
      onStart: () => state = state.copyWith(
        status: reset ? AppStatus.loading : AppStatus.loadingMore,
        data: reset ? null : state.data,
        hasMore: reset ? true : state.hasMore,
        currentPage: currentPage,
      ),
      onSuccess: (CategoryResponse response) {
        state = state.copyWith(status: AppStatus.success,data: response.data?.categories);

      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to fetch categories',
        );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
        print('Categories fetch error: $e');
      },
      onComplete: () => print('Categories fetch completed'),
    );
  }
}