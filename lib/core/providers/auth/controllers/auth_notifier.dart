import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import '../../../data/request_query/auth_request.dart';
import '../../../helpers/local_storage_service.dart';
import '../../../network/app_status.dart';
import '../../../network/base_notifier.dart';
import '../../../network/network_utils.dart';
import '../../../utils/Utils.dart';
import '../../../utils/device_utils.dart';

class AuthNotifier extends BaseNotifier<void> {
  AuthNotifier(Ref ref) : super(ref, const AppState<void>()) {
    _initialize();
  }

  Future<void> _initialize() async {


    callDataService(
      repo.getPartner(),
      onStart: () => print('Loading partner...'),
      onSuccess: (response) =>
          print('Partner loaded: {${response.toString()}}'),
      onError: (e) => print('Error: $e'),
      onComplete: () => print('Partner load complete'),
    );
  }

  void setLoggedIn(bool isLoggedIn) {
    state = state.copyWith(
      status: isLoggedIn ? AppStatus.success : AppStatus.idle,
    );
  }

  Future<void> authenticate(
      BuildContext context, String username, String password) async {
    callDataService(
      repo.login(AuthQueryParam(
        username: username,
        password: password,
      )),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (response) async =>
          await _handleSuccessResponse(context, response, username, password),
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Login failed',
        );
        Utils().showFlushbar(context, message: state.errorMessage!,isError: true);      },
    );
  }

  Future<void> _handleSuccessResponse(BuildContext context, dynamic response, String username, String password) async {

    state = state.copyWith(status: AppStatus.success);
    if (context.mounted) {
      await localStorage.saveLoginData(
        loginModel: response,
        email: username,
        password: password,
      );
      context.go('/home');
    }
  }


   Future<void> logout(BuildContext context) async {
    callDataService(
      repo.logout(),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (response) async =>
      await _handleSuccessLogoutResponse(context, response),
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Login failed',
        );
        Utils().showSnackBar(context, state.errorMessage!);
      },
    );
  }

  Future<void> _handleSuccessLogoutResponse(
      BuildContext context, dynamic response) async {
    state = state.copyWith(status: AppStatus.success);
    if (context.mounted) {
      // await localStorage.saveLoginData(
      //   loginModel: response
      // );
      context.go('/splash');
      print('Logout button pressed!');
      final localStorage = ref.read(localStorageProvider);
      await localStorage.clearLoginData();
    }
  }




  void reset() {
    state = const AppState<void>();
  }
}
