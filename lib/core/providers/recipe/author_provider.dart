import 'dart:io';

import '../../../app/imports/packages_imports.dart';

final authorProvider =
    StateNotifierProvider<AuthorNotifier, AuthorState>((ref) {
  return AuthorNotifier();
});

class AuthorState {
  final String? authorName;
  final String? source;
  final String? copyright;
  final File? image;

  const AuthorState({
    this.authorName,
    this.source,
    this.copyright,
    this.image,
  });

  // Add copyWith method for easier state updates
  AuthorState copyWith({
    String? authorName,
    String? source,
    String? copyright,
    File? image,
  }) {
    return AuthorState(
      authorName: authorName ?? this.authorName,
      source: source ?? this.source,
      copyright: copyright ?? this.copyright,
      image: image ?? this.image,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthorState &&
        other.authorName == authorName &&
        other.source == source &&
        other.copyright == copyright &&
        other.image?.path == image?.path; // Compare file paths
  }

  @override
  int get hashCode {
    return authorName.hashCode ^
        source.hashCode ^
        copyright.hashCode ^
        (image?.path.hashCode ?? 0);
  }
}

class AuthorNotifier extends StateNotifier<AuthorState> {
  AuthorNotifier() : super(const AuthorState());

  void updateAuthorName(String value) {
    state = AuthorState(
      authorName: value,
      source: state.source,
      copyright: state.copyright,
      image: state.image,
    );
  }

  void updateSource(String value) {
    state = AuthorState(
      authorName: state.authorName,
      source: value,
      copyright: state.copyright,
      image: state.image,
    );
  }

  void updateCopyright(String value) {
    state = AuthorState(
      authorName: state.authorName,
      source: state.source,
      copyright: value,
      image: state.image,
    );
  }

  void updateImage(File file) {
    print("AuthorNotifier: Updating image with file: ${file.path}");
    print("AuthorNotifier: File exists: ${file.existsSync()}");

    // Use copyWith to ensure proper state change detection
    state = state.copyWith(image: file);

    print("AuthorNotifier: State updated. Current image: ${state.image?.path}");
  }

  /// Reset all author data to initial state
  void reset() {
    print("AuthorNotifier: Resetting to initial state");
    state = const AuthorState();
  }

  /// Clear only the image while keeping other data
  void clearImage() {
    print("AuthorNotifier: Clearing image");
    state = state.copyWith(image: null);
  }
}
