import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';

final mediaFilesProvider = StateNotifierProvider<MediaFilesNotifier, ({List<RecipeMedia> mediaFiles, int coverIndex})>((ref) {
  return MediaFilesNotifier();
});

class MediaFilesNotifier extends StateNotifier<({List<RecipeMedia> mediaFiles, int coverIndex})> {
  MediaFilesNotifier() : super((mediaFiles: [], coverIndex: 1));

  void updateMedia(List<RecipeMedia?> files, int coverIndex) {
    final newState = files.where((file) => file != null).cast<RecipeMedia>().toList();
    print('Updating mediaFilesProvider with ${newState.length} items: ${newState.map((m) => m.toJson()).toList()}, coverIndex: $coverIndex');
    state = (mediaFiles: newState, coverIndex: coverIndex);
  }

  void clear() {
    print('Clearing mediaFilesProvider');
    state = (mediaFiles: [], coverIndex: 1);
  }

  void set(List<RecipeMedia>? files) {
    final newState = files ?? [];
    int newCoverIndex = state.coverIndex;
    print('Setting mediaFilesProvider with ${newState.length} items: ${newState.map((m) => m.toJson()).toList()}');
    state = (mediaFiles: newState, coverIndex: newCoverIndex);
  }

  void setCoverIndex(int index) {
    if (index != 0 && index < 9) { // Prevent setting cover to index 0 (video) and ensure valid index
      print('Setting coverIndex to $index');
      state = (mediaFiles: state.mediaFiles, coverIndex: index);
    }
  }
}