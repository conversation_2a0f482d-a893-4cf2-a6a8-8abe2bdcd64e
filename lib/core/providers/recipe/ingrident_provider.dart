import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final ingredientsProvider = StateNotifierProvider<IngredientsNotifier, List<String>>((ref) {
  return IngredientsNotifier();
});

class IngredientsNotifier extends StateNotifier<List<String>> {
  IngredientsNotifier() : super([]) {
    print("IngredientsNotifier initialized with state: $state");
  }

  final List<TextEditingController> _controllers = [];

  List<TextEditingController> get controllers => _controllers;

  void addController() {
    _controllers.add(TextEditingController());
    state = [...state, ''];
    print("Added controller. New state: $state");
  }

  void removeController(int index) {
    if (index >= 0 && index < _controllers.length) {
      _controllers[index].dispose();
      _controllers.removeAt(index);
      state = List.from(state)..removeAt(index);
      print("Removed controller at index $index. New state: $state");
    }
  }

  void setIngredients() {
    state = _controllers
        .map((controller) => controller.text.trim())
        .where((text) => text.isNotEmpty)
        .toList();
    print("Set ingredients. New state: $state");
  }

  void updateIngredients(List<String> backendIngredients) {
    print("Updating ingredients with: $backendIngredients");
    for (var controller in _controllers) {
      controller.dispose();
    }
    _controllers.clear();
    _controllers.addAll(
      backendIngredients.map((ingredient) => TextEditingController(text: ingredient)),
    );
    state = backendIngredients.where((ingredient) => ingredient.trim().isNotEmpty).toList();
    print("Updated state: $state");
    print("Controllers: ${_controllers.map((c) => c.text).toList()}");
  }

  void clearIngredients() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    _controllers.clear();
    state = [];
    print("Cleared ingredients. New state: $state");
  }

  void resetWithDefaults({int defaultCount = 5}) {
    clearIngredients();
    for (int i = 0; i < defaultCount; i++) {
      _controllers.add(TextEditingController());
      state = [...state, ''];
    }
    print("Reset with $defaultCount empty controllers. New state: $state");
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
    print("IngredientsNotifier disposed");
  }
}