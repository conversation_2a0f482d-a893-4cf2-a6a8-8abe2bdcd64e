import 'package:flutter/material.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/get_ask_ai_thread_messages.dart';
import 'package:mastercookai/core/providers/gpt/thread_messages_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/network/network_utils.dart';

import '../../../app/imports/packages_imports.dart';
import '../../data/models/get_ask_ai_response.dart';
import '../../data/models/ask_ai_response.dart';
import '../../data/request_query/ask_ai_request.dart';
import '../../data/request_query/paginantion_request.dart';
import '../../data/request_query/delete_thread_request.dart';
import '../../data/models/base_response.dart';
import '../../network/app_status.dart';
import '../../network/base_notifier.dart';

final askAiNotifierProvider =
StateNotifierProvider<AskAiNotifier, AppState<List<Thread>>>(
      (ref) => AskAiNotifier(ref),
);

class AskAiNotifier extends BaseNotifier<List<Thread>> {
  String? _currentSearchQuery;
  static const int pageSize = 20; // Define page size as a constant

  AskAiNotifier(Ref ref) : super(ref, const AppState<List<Thread>>()) {
    _initialize();
  }

  void _initialize() {
    // Initial fetch of AskAiThreads
    callDataService(
      repo.getAskAiThreads(PaginationQueryParam(
        pageNumber: 1,
        pageSize: pageSize,
      )),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (response) => _handleSuccessResponse(response, false),
      onError: (e) => state = state.copyWith(
        status: AppStatus.error,
        errorMessage: e.message ?? 'Failed to fetch threads',
      ),
      onComplete: () => print('Threads fetch complete'),
    );
  }

  // Post - Add AskAi
  Future<void> askAi(BuildContext context, int? selectedThreadId, String prompt,
      {Function(ThreadData)? onNewThreadCreated,
        Function(ThreadData)? onResponse}) async {
    // Set thread ID to 0 if no thread is selected, otherwise use the selected thread ID
    final threadId = selectedThreadId ?? 0;

    // Create and add the user's message to the thread messages provider
    final userMessage = UserMessage(
    prompt: prompt,
    id: threadId, response: '', askedAt: '',

    );
    ref.read(threadMessagesNotifierProvider.notifier).addNewMessage(userMessage);

    callDataService(
      repo.askAi(AskAiQueryParam(
        threadId: threadId,
        prompt: prompt,
      )),
      onStart: () => state = state.copyWith(status: AppStatus.creating),
      onSuccess: (response) async {
        if (response.success == false) {
          if (context.mounted) {
            Utils().showFlushbar(context,
                message: response.message.error ?? 'Failed to send message',
                isError: true);
          }
          // Close the dialog if open
          state = state.copyWith(
              status: AppStatus.createError,
              errorMessage: response.message.error);
          return;
        }

        // After successful API response, append ThreadData to UserMessage
        final threadData = response.data;
        final newUserMessage =
        UserMessage.fromThreadData(threadData, image: threadData.imageUrl);
        ref.read(threadMessagesNotifierProvider.notifier).removeLastMessage();
        // Add the AI response to the thread messages provider
        ref.read(threadMessagesNotifierProvider.notifier).addNewMessage(newUserMessage);

        // Always call onResponse callback with thread data
        if (onResponse != null) {
          onResponse(threadData);
        }

        // If this was a new thread (threadId == 0), refresh the threads list to get the new thread
        if (threadId == 0) {
          await getAskAiThreads(context: context);
          // Notify the UI about the new thread creation
          if (onNewThreadCreated != null) {
            onNewThreadCreated(threadData);
          }
        }

        state = state.copyWith(status: AppStatus.createSuccess);
      },
      onError: (e) {
        state = state.copyWith(
            status: AppStatus.createError, errorMessage: e.message);
        if (context.mounted) {
          //Utils().showSnackBar(context, state.errorMessage!);
           Utils().showFlushbar(context, message: "We are unable to process your request. Please try again later.", isError: true);
        }
      },
    );
  }

  /// GET - Fetch threads with pagination and search
  Future<void> getAskAiThreads({
    required BuildContext context,
    bool loadMore = false,
    String? search,
  }) async {
    // If this is a new search, reset pagination
    if (search != _currentSearchQuery) {
      _currentSearchQuery = search;
      loadMore = false;
    }

    if (loadMore && (!state.hasMore || state.status == AppStatus.loadingMore)) {
      return;
    }

    final currentPage = loadMore ? state.currentPage + 1 : 1;

    callDataService(
      repo.getAskAiThreads(PaginationQueryParam(
        pageNumber: currentPage,
        pageSize: pageSize,
        search: _currentSearchQuery,
      )),
      onStart: () => state = state.copyWith(
        status: loadMore ? AppStatus.loadingMore : AppStatus.loading,
        data: loadMore ? state.data : [],
        hasMore: loadMore ? state.hasMore : true,
        currentPage: currentPage,
      ),
      onSuccess: (response) => _handleSuccessResponse(response, loadMore),
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to fetch threads',
        );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
      },
      onComplete: () => print('Threads fetch completed'),
    );
  }

  /// Search threads
  Future<void> searchThreads({
    required BuildContext context,
    required String query,
  }) async {
    await getAskAiThreads(
      context: context,
      loadMore: false,
      search: query.trim().isEmpty ? null : query.trim(),
    );
  }

  /// Clear search and reload all threads
  Future<void> clearSearch({required BuildContext context}) async {
    _currentSearchQuery = null;
    await getAskAiThreads(context: context, loadMore: false);
  }

  /// DELETE - Delete threads
  Future<void> deleteThread({
    required BuildContext context,
    required String type,
    required List<int> threadIds,
  }) async {
    callDataService(
      repo.deleteThread(DeleteThreadRequest(
        type: type,
        threadIds: threadIds,
      )),
      onStart: () => state = state.copyWith(status: AppStatus.deleting),
      onSuccess: (BaseResponse response) {
        if (response.success == true) {
          // Remove deleted threads from the current state
          final currentThreads = state.data ?? [];
          final updatedThreads = currentThreads
              .where((thread) => !threadIds.contains(thread.id))
              .toList();

          state = state.copyWith(
            status: AppStatus.deleteSuccess,
            data: updatedThreads,
            totalItems: state.totalItems - threadIds.length,
            errorMessage: null,
          );

          if (context.mounted) {
            Utils().showFlushbar(
              context,
              message: response.message?.general?.isNotEmpty == true
                  ? response.message!.general!.first
                  : 'Threads deleted successfully',
              isError: false,
            );
          }
        } else {
          state = state.copyWith(
            status: AppStatus.deleteError,
            errorMessage: response.message?.error?.isNotEmpty == true
                ? response.message!.error!.first
                : 'Failed to delete threads',
          );

          if (context.mounted) {
            Utils().showFlushbar(
              context,
              message: response.message?.error?.isNotEmpty == true
                  ? response.message!.error!.first
                  : 'Failed to delete threads',
              isError: true,
            );
          }
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.deleteError,
          errorMessage: e.message ?? 'Failed to delete threads',
        );

        if (context.mounted) {
          Utils().showFlushbar(
            context,
            message: state.errorMessage!,
            isError: true,
          );
        }
      },
      onComplete: () => print('Delete threads operation completed'),
    );
  }

  void _handleSuccessResponse(GetAskAiResponse response, bool loadMore) {
    final result = response.data;
    final currentPage = state.currentPage;
    final hasMore = currentPage < result.totalPageCount;

    print(
        'Pagination Debug: currentPage=$currentPage, totalPageCount=${result.totalPageCount}, hasMore=$hasMore, threadsCount=${result.threads.length}');

    state = state.copyWith(
      status: result.threads.isEmpty && !loadMore
          ? AppStatus.empty
          : AppStatus.success,
      data:
      loadMore ? [...state.data ?? [], ...result.threads] : result.threads,
      hasMore: hasMore,
      currentPage: currentPage,
      totalItems: result.totalRecords,
      errorMessage: null,
    );
  }

  @override
  void reset() {
    state = const AppState<List<Thread>>();
  }

  void resetToIdle() {
    state = state.copyWith(status: AppStatus.idle, errorMessage: null);
  }
}