import '../../../app/imports/packages_imports.dart';
import '../../data/models/plans_response.dart';
import '../../network/app_status.dart';
import '../../network/base_notifier.dart';
import '../../network/network_utils.dart';

/// Provider for managing plans operations
final plansNotifierProvider =
StateNotifierProvider<PlansNotifier, AppState<PlansData?>>(
      (ref) => PlansNotifier(ref),
);


class PlansNotifier extends BaseNotifier<PlansData?> {
  PlansNotifier(Ref ref) : super(ref, const AppState<PlansData?>());

  /// Fetch plans data
  Future<void> fetchPlans() async {
    callDataService(
      repo.plans(),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (PlansResponse response) {
        if (response.success == true && response.data != null) {
          state = state.copyWith(
            status: AppStatus.success,
            data: response.data!,
            errorMessage: null,
          );
        } else {
          // Handle case where API returns success but no plans data
          state = state.copyWith(
            status: AppStatus.empty,
            data: null,
            errorMessage: response.message?.general?.isNotEmpty == true
                ? response.message!.general!.first
                : 'No plans data found',
          );
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
          data: null,
        );
      },
      onComplete: () {}, // Optional completion callback
    );
  }

  /// Get loading status
  bool get isLoading => state.status == AppStatus.loading;

  /// Get success status
  bool get isSuccess => state.status == AppStatus.success;

  /// Get error status
  bool get hasError => state.status == AppStatus.error;

  /// Get empty status
  bool get isEmpty => state.status == AppStatus.empty;

  /// Get error message
  String? get errorMessage => state.errorMessage;

  /// Get plans list
  List<Plan> get plans => state.data?.plans ?? [];

  /// Check if plans are available
  bool get hasPlans => plans.isNotEmpty;
  bool isFieldsPopulated = false;
}
