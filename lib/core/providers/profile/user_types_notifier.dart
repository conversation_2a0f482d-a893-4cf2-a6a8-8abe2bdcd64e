import '../../../app/imports/packages_imports.dart';
import '../../data/models/user_types_response.dart';
import '../../network/app_status.dart';
import '../../network/base_notifier.dart';
import '../../network/network_utils.dart';

/// Provider for managing user types operations
final userTypesNotifierProvider =
StateNotifierProvider<UserTypesNotifier, AppState<UserTypesData?>>(
      (ref) => UserTypesNotifier(ref),
);


class UserTypesNotifier extends BaseNotifier<UserTypesData?> {
  UserTypesNotifier(Ref ref) : super(ref, const AppState<UserTypesData?>());

  /// Fetch user types
  Future<void> fetchUserTypes() async {
    callDataService(
      repo.userTypes(),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (UserTypesResponse response) {
        if (response.success && response.data != null) {
          state = state.copyWith(
            status: AppStatus.success,
            data: response.data!,
            errorMessage: null,
          );
        } else {
          state = state.copyWith(
            status: AppStatus.empty,
            data: null,
            errorMessage: response.message.general.isNotEmpty
                ? response.message.general.first
                : 'No user types data found',
          );
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
          data: null,
        );
      },
      onComplete: () {},
    );
  }

  /// Reset the notifier state to initial state
  void reset() {
    state = const AppState<UserTypesData?>();
  }

  /// Get current user types data
  UserTypesData? get userTypesData => state.data;

  /// Get user types list
  List<UserType> get userTypes => state.data?.userTypes ?? [];

  /// Check if currently loading
  bool get isLoading => state.status == AppStatus.loading;

  /// Check if user types are loaded
  bool get hasUserTypes =>
      state.data?.userTypes.isNotEmpty == true && state.status == AppStatus.success;

  /// Check if there's an error
  bool get hasError => state.status == AppStatus.error;

  /// Check if user types list is empty
  bool get isEmpty => state.status == AppStatus.empty || state.data == null;

  /// Get error message
  String? get errorMessage => state.errorMessage;
}