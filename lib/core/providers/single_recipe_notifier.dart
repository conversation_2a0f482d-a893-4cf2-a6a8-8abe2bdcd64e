import 'package:flutter/material.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import '../../../app/imports/packages_imports.dart';
import '../data/models/category_response.dart';
import '../data/models/cuisines_response.dart';
import '../data/models/nutritionInfo_response.dart';
import '../data/models/recipe_response.dart';
import '../network/base_notifier.dart';
import '../network/network_utils.dart';

// Provider for SingleRecipeNotifier
final singleRecipeNotifierProvider =
    StateNotifierProvider<SingleRecipeNotifier, AppState<RecipeDetails>>(
  (ref) => SingleRecipeNotifier(ref),
);

class SingleRecipeNotifier extends BaseNotifier<RecipeDetails> {
  SingleRecipeNotifier(Ref ref) : super(ref, const AppState<RecipeDetails>());

  Future<void> goToEditRecipeScreen(
      BuildContext context,
      int recipeId,
      int cookbookId,
      List<Recipe> recipesList,
      RecipeDetails recipeDetails,
      List<Categories> categories,
      List<Cuisines> cuisines) async {
    await context.push(
      '/cookbook/cookbookDetail/recipeDetail/$recipeId/EditRecipeScreen',
      extra: {
        'recipeId': recipeId,
        'cookbookId': cookbookId,
        'recipesList': recipesList,
        'recipeDetails': recipeDetails,
        'categories': categories,
        'cuisines': cuisines,
      },
    );
  }

  Future<void> fetchRecipe({
    required BuildContext context,
    required int cookbookId,
    required int recipeId,
  }) async {
    callDataService(
      repo.getRecipeDetail(cookbookId: cookbookId, recipeId: recipeId),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (RecipeDetailResponse response) {
        state = state.copyWith(
            status: AppStatus.success, data: response.data!.recipeDetails);
      },
      onError: (e) {
        state = state.copyWith(status: AppStatus.error);
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
        print('Recipe fetch error: $e');
      },
      onComplete: () => print('Recipe fetch completed'),
    );
  }
}
