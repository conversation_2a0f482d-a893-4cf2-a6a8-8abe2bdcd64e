// lib/core/providers/shopping/pantry_item_notifier.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/core/data/models/pantry.dart';
import 'package:mastercookai/core/data/models/pantry_response.dart';
import 'package:mastercookai/core/data/partner_api.dart';
import 'package:mastercookai/core/data/request_query/pantry_item_request.dart';
import 'package:mastercookai/core/data/request_query/paginantion_request.dart';
import 'package:mastercookai/core/helpers/app_constant.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import '../../data/models/pantry_listI_iem_response.dart';
import '../../network/app_status.dart';
import '../../network/base_notifier.dart';
import '../../network/network_utils.dart';

final pantryItemNotifierProvider =
StateNotifierProvider<PantryItemNotifier, AppState<PantryListItemData>>(
      (ref) => PantryItemNotifier(ref),
);

class PantryItemNotifier extends BaseNotifier<PantryListItemData> {
  PantryItemNotifier(Ref ref)
      : super(ref, const AppState<PantryListItemData>());

  bool get canFetchMore => state.hasMore && state.status != AppStatus.loadingMore;

  /// Fetch pantry list items with pagination
  Future<bool> fetchPantryItems({
    BuildContext? context,
    required int id,
    String? search,
    bool loadMore = false,
  }) async {
   // final loadMore = state.data != null && state.data!.pantryItems.isNotEmpty;
    if (loadMore && (!state.hasMore || state.status == AppStatus.loadingMore)) {
      return false;
    }

    final currentPage = loadMore ? state.currentPage + 1 : 1;
    bool isSuccess = false;

    final paginationParam = PaginationQueryParam(
      pageNumber: currentPage,
      pageSize: 20,
    );

    if (search != null && search.isNotEmpty) {
      paginationParam.search = search;
    }
    
    showLoader();
    await callDataService(
      ref.read(partnerApiProvider).fetchPantryListsItems(id, paginationParam),
      onStart: () => state = state.copyWith(
        status: loadMore ? AppStatus.loadingMore : AppStatus.loading,
        data: loadMore ? state.data : null,
        hasMore: loadMore ? state.hasMore : true,
        currentPage: currentPage,
      ),
      onSuccess: (response) {
        _handlePantryItemSuccessResponse(response as PantryListItemResponse, loadMore);
        isSuccess = true;
        hideLoader();
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.toString(),
        );
        if (context?.mounted == true) {
          Utils().showFlushbar(context!, message: e.toString(), isError: true);
        }
        isSuccess = false;
        hideLoader();
      },
      onComplete: () => {},
    );

    return isSuccess;
  }

  /// Add pantry items
  Future<(bool, String?)> addPantryItems({
    required int pantryListId,
    required PantryItemsRequest request,
  }) async {
    if (request.pantryItems?.isEmpty ?? true) {
      const errorMessage = 'No pantry items provided';
      return (false, errorMessage);
    }

    state = state.copyWith(status: AppStatus.loading);

    try {
      final repositoryImpl = ref.read(partnerApiProvider);
      final success = await repositoryImpl.addPantryItems(pantryListId, request);

      if (success) {
        state = state.copyWith(status: AppStatus.success, errorMessage: null);
        return (true, null);
      } else {
        const errorMessage = 'Failed to add pantry items';
        state = state.copyWith(status: AppStatus.error, errorMessage: errorMessage);
        return (false, errorMessage);
      }
    } catch (e) {
      final errorMessage = _handleError(e);
      state = state.copyWith(status: AppStatus.error, errorMessage: errorMessage);
      return (false, errorMessage);
    }
  }

  /// Delete pantry items
  Future<(bool, String?)> deletePantryItems({
    required int pantryListId,
    required DeletePantryItemsRequest request,
  }) async {
    if (request.type != "All" && (request.pantryItemIds?.isEmpty ?? true)) {
      const errorMessage = 'No pantry item IDs provided';
      return (false, errorMessage);
    }

    state = state.copyWith(status: AppStatus.loading);

    try {
      final repositoryImpl = ref.read(partnerApiProvider);
      final response = await repositoryImpl.deletePantryItems(pantryListId, request);

      if (response.success == true) {
        state = state.copyWith(status: AppStatus.success, errorMessage: null);
        return (true, null);
      } else {
        final errorMessage = response.message?.general?.isNotEmpty == true
            ? response.message!.general!.first
            : 'Failed to delete pantry items';
        state = state.copyWith(status: AppStatus.error, errorMessage: errorMessage);
        return (false, errorMessage);
      }
    } catch (e) {
      final errorMessage = _handleError(e);
      state = state.copyWith(status: AppStatus.error, errorMessage: errorMessage);
      return (false, errorMessage);
    }
  }

  void _handlePantryItemSuccessResponse(PantryListItemResponse response, bool loadMore) {
    final result = response.data;
    state = state.copyWith(
      status: result.pantryItems.isEmpty && !loadMore
          ? AppStatus.empty
          : AppStatus.success,
      data: loadMore
          ? PantryListItemData(
        pantryItems: [...state.data?.pantryItems ?? [], ...result.pantryItems],
        totalRecords: result.totalRecords,
        totalPageCount: result.totalPageCount,
      )
          : PantryListItemData(
        pantryItems: result.pantryItems,
        totalRecords: result.totalRecords,
        totalPageCount: result.totalPageCount,
      ),
      hasMore: result.pantryItems.length == 20,
      currentPage: state.currentPage,
      totalItems: result.totalPageCount.toInt(),
      errorMessage: null,
    );
  }

  String _handleError(dynamic error) {
    if (error is Exception) {
      return error.toString().replaceAll('Exception: ', '');
    } else {
      return 'An unexpected error occurred: ${error.toString()}';
    }
  }

  @override
  void reset() {
    state = const AppState<PantryListItemData>();
  }

  void resetToIdle() {
    state = state.copyWith(status: AppStatus.idle, errorMessage: null);
  }
}
