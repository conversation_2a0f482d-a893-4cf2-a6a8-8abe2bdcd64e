import 'dart:developer';

import 'package:floating_bottom_bar/animated_bottom_navigation_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../assets_manager.dart';
import '../imports/packages_imports.dart'; // Ensure this includes go_router and other necessary imports

class CustomBottomNavScreen extends HookConsumerWidget {
  final StatefulNavigationShell navigationShell;

  const CustomBottomNavScreen({super.key, required this.navigationShell});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = useState(0); // "Home" is selected by default (index 0)

    // Use only 4 icons to match the 4 labels
    final List<String> iconAssets = const [
      AssetsManager.home_svg, // Home
      AssetsManager.cookbook_svg, // Cookbooks
      AssetsManager.shopping_svg, // Recipes
      AssetsManager.myProfile, // Shop List
    ];

    // Labels to match the image
    final List<String> labels = [
      'Home',
      'Cookbooks',
      'Recipes',
      'Shop List',
    ];

    void onTabSelected(int index) {
      if (currentIndex.value != index) {
        currentIndex.value = index;
        navigationShell.goBranch(index); // Switch to the selected navigation branch
      }
    }

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: navigationShell,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: AnimatedBottomNavigationBar(
        barColor: Colors.white,
        controller: FloatingBottomBarController(initialIndex: 0),
        bottomBar: List.generate(iconAssets.length, (index) {
          return BottomBarItem(
            icon: SvgPicture.asset(
              iconAssets[index],
              width: 25,
              height: 25,
             // color: Colors.black, // Default color for unselected icon
            ),
            iconSelected: SvgPicture.asset(
              iconAssets[index],
              width: 25,
              height: 25,
            //  color: Colors.red, // Color for selected icon
            ),
            title: labels[index],
            titleStyle: TextStyle(
              color:  Colors.black,
              fontSize: 12,
            ),
            dotColor: Colors.red,
            onTap: (value) => onTabSelected(index),
          );
        }),
        bottomBarCenterModel: BottomBarCenterModel(
          centerBackgroundColor: Colors.red,
          centerIcon: const FloatingCenterButton(
            child: Icon(
              Icons.add,
              color: AppColors.white,
            ),
          ),
          centerIconChild: [
            FloatingCenterButtonChild(
              child: Column(
                children: [
                  const Icon(
                    Icons.home,
                    color: AppColors.white,
                  ),
                  // CustomText(
                  //   text: 'Home',
                  //   color: AppColors.white,
                  //   size: 12,
                  // ),
                ],
              ),
              onTap: () => log('Home'),
            ),
            FloatingCenterButtonChild(
              child: Column(
                children: [
                  const Icon(
                    Icons.book,
                    color: AppColors.white,
                  ),
                  // CustomText(
                  //   text: 'Cookbooks',
                  //   color: AppColors.white,
                  //   size: 12,
                  // ),
                ],
              ),
              onTap: () => log('Cookbooks'),
            ),
            FloatingCenterButtonChild(
              child: Column(
                children: [
                  const Icon(
                    Icons.restaurant_menu,
                    color: AppColors.white,
                  ),
                  // CustomText(
                  //   text: 'Recipes',
                  //   color: AppColors.white,
                  //   size: 12,
                  // ),
                ],
              ),
              onTap: () => log('Recipes'),
            ),
            FloatingCenterButtonChild(
              child: Column(
                children: [
                  const Icon(
                    Icons.shopping_cart,
                    color: AppColors.white,
                  ),
                  // CustomText(
                  //   text: 'Shop List',
                  //   color: AppColors.white,
                  //   size: 12,
                  // ),
                ],
              ),
              onTap: () => log('Shop List'),
            ),
          ],
        ),
      ),
    );
  }
}