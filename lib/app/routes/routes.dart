import 'package:go_router/go_router.dart';
import '../../presentation/auth/login_screen.dart';
import '../../presentation/auth/splash_screen.dart';
import '../imports/core_imports.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'personal_routes.dart';

final rootNavigatorKey = GlobalKey<NavigatorState>();
final shellNavigatorKey = GlobalKey<NavigatorState>();

/// This will be replaced with a real auth provider
final authProvider = StateProvider<bool>((ref) => false);

final goRouterProvider = Provider<GoRouter>((ref) {
  final isAuthenticated = ref.watch(authProvider);

  return GoRouter(
    navigatorKey: rootNavigatorKey,
    initialLocation: '/splash',
    debugLogDiagnostics: true,


    routes: [
      /// Splash Screen
      GoRoute(
        path: Routes.splash,
        builder: (context, state) => SplashScreen(),
      ),

      /// Login Screen
      GoRoute(
        path: Routes.login,
        builder: (context, state) => const LoginScreen(),
      ),
      ...personalRoutes,
    ],
  );
});
