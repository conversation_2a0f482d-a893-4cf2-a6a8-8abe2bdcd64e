import '../imports/core_imports.dart';

class AppColors {
  static const Color primaryColor = Color(0xFFE01010);
  static const Color secondaryColor = Color(0xFFFFFFFF);
  static const Color tertiaryColor = Color(0xFF39D2C0);
  static const Color lightPrimaryBackgroundColor = Color(0xFFFFFFFF);
  static const Color backgroudInActiveColor = Color(0xFF77777E);
  static const Color lightSecondaryBackgroundColor = Color(0xFFF1F4F8);
  static const Color whiteColor = Color(0xFFFFFFFF);
  static const Color darkPrimaryBackgroundColor = Color(0xFF1A1F24);
  static const Color borderColor = Color(0xff268eff99);

  static const Color primaryLightTextColor = Color(0xFF4F4F4F);
  static const Color primaryGreyColor = Color(0xFF333333);
  static const Color primaryHintColor = Color(0xFFFEFEFE);
  static const Color primaryBorderColor = Color(0xFF268EFF);

  static const Color lightGreyColor = Color(0xFFBDBDBD);
  static const Color lightestGreyColor = Color(0xFFE0E0E0);

  static const Color successColor = Color(0xFF60C26F);
  static const Color errorColor = Color(0xFFE74852);
  static const Color warningColor = Color(0xFFCA6C45);

  static const Color greyBorderColor = Color(0xFFBDBDBD);

  static const Color textGreyColor = Color(0xFF828282);

  static const Color greyCardColor = Color(0xFFF3F3F3);

  static const Color lightgreenCardColor = Color(0xFFD8ECF1);

  static const Color blackTextColor = Color(0xFF1D1D1D);
  static const Color blackColor = Color(0xFF000000);

  static const Color yellowRowColor = Color(0xFFFFFAE3);
  static const Color blueRowColor = Color(0xffc1dfff3d);
  static const Color texGreyColor = Color(0xFF333333);
  static const Color texLightBlackColor = Color(0xD9000000);
  static const Color selectionColor = Color(0x81268EFF);
  static const Color selectionMealPortion = Color(0x1A268EFF);
  static const Color selctBoarderColor = Color(0xFFE0E0E0);





}
