import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:mastercookai/core/data/models/base_response.dart';
import 'package:mastercookai/core/data/models/plans_response.dart';
import 'package:mastercookai/core/data/models/recipe_response.dart';
import 'package:mastercookai/core/data/models/search_items_response.dart';
import 'package:mastercookai/core/data/models/sync_list_response.dart';
import 'package:mastercookai/core/data/models/user_profile_response.dart';
import 'package:mastercookai/core/data/models/user_types_response.dart';
import 'package:mastercookai/core/data/request_query/ask_ai_request.dart';
import 'package:mastercookai/core/data/request_query/auth_request.dart';
import 'package:mastercookai/core/data/request_query/delete_thread_request.dart';
import 'package:mastercookai/core/data/request_query/fetch_cookbook_request.dart';
import 'package:mastercookai/core/data/request_query/paginantion_request.dart';
import 'package:mastercookai/core/data/request_query/pantry_item_request.dart';
import 'package:mastercookai/core/data/request_query/shopping_item_request.dart';
import 'package:mastercookai/core/data/request_query/update_profile_request.dart';
import 'package:mastercookai/domain/repositories/repository.dart';
import '../../core/data/models/ask_ai_response.dart';
import '../../core/data/models/create_shopping_list_response.dart';
import '../../core/data/models/get_ask_ai_response.dart';
import '../../core/data/models/category_response.dart';
import '../../core/data/models/cookbook.dart';
import '../../core/data/models/create_recipe_response.dart';
import '../../core/data/models/cuisines_response.dart';
import '../../core/data/models/forgot_password_response.dart';
import '../../core/data/models/get_ask_ai_thread_messages.dart' hide MessageResponse;
import '../../core/data/models/home_model.dart';
import '../../core/data/models/login_model.dart';
import '../../core/data/models/nutritionInfo_response.dart';
import '../../core/data/models/pantry_listI_iem_response.dart';
import '../../core/data/models/pantry_response.dart';
import '../../core/data/models/partner_model.dart';
import '../../core/data/models/recipe_delete_response.dart';
import '../../core/data/models/recipe_detail_response.dart';
import '../../core/data/models/shopping_response.dart';
import '../../core/data/request_query/create_recipe_request.dart';

import '../../core/data/request_query/home_request.dart';
import '../../core/helpers/access_key_helper.dart';
import '../../core/helpers/app_constant.dart';
import '../../core/helpers/local_storage_service.dart';
import '../../core/network/api_endpoint.dart';
import '../../core/network/base_repository_source.dart';
import '../../core/utils/HeaderBuilder.dart';
import 'package:mastercookai/core/data/models/shopping.dart';

class RepositoryImplementation extends BaseRepositorySource
    implements Repository {
  final Dio dio;
  final LocalStorageService localStorage;

  RepositoryImplementation(this.dio, this.localStorage);

  Future<Map<String, String>> buildCommonHeaders() async {
    final accessKey = await AccessKeyHelper.getAccessKey();
    final packageName = AccessKeyHelper.getPlatformIdentifier();
    final apiKey = await localStorage.getApiKey();
    final token = await localStorage.getToken();

    final headerBuilder = await HeaderBuilder.initialize();
    // If you need to call any async method on headerBuilder, do it here.
    final Map<String, String> headers =
        Map<String, String>.from(headerBuilder.build());
    if (apiKey != null && apiKey.isNotEmpty) {
      final encodedKey = base64Encode(utf8.encode(apiKey));
      headers['api-key'] = encodedKey;
      if (token != null && token.isNotEmpty) {
        headers['Authorization'] = 'Bearer $token';
        bearerToken = token;
      }
      print('Token===> $token');
      // 'Authorization': 'Bearer $token',
    }
    if (packageName != null && packageName.isNotEmpty) {
      headers['packageName'] = packageName;
    }
    if (accessKey != null && accessKey.isNotEmpty) {
      headers['accessKey'] = accessKey;
    }
    return headers;
  }

  @override
  Future<bool> getPartner() async {
    final accessKey = await AccessKeyHelper.getAccessKey();
    final packageName = AccessKeyHelper.getPlatformIdentifier();
    if (accessKey == null) {
      throw Exception("No access key found for current platform");
    }
    final headers = await buildCommonHeaders();

    final response = await dioClient.post('api/auth/partner',
        data: {
          'packageName': packageName,
          'accessKey': accessKey,
        },
        options: Options(headers: headers));
    if (response.statusCode == 200) {
      final partnerModel = PartnerModel.fromJson(response.data);
      final apiKey = partnerModel.data?.apiKey;

      if (partnerModel.success && apiKey != null && apiKey.isNotEmpty) {
        await localStorage.saveApiKey(apiKey);
        return true;
      }
    }
    return false;
  }

  @override
  Future<LoginModel> login(AuthQueryParam queryParam) async {
    final headers = await buildCommonHeaders();

    try {
      final response = await callApiWithErrorParser(
        dioClient.post(
          ApiEndpoints.login,
          data: queryParam.toJson(),
          options: Options(headers: headers),
        ),
      );
      return LoginModel.fromJson(response.data);
    } on DioError catch (dioError) {
      if (dioError.response?.statusCode == 403) {
        // Handle 403 error explicitly
        throw Exception(
            "Access Denied: Invalid credentials or forbidden access.");
      } else {
        // Re-throw other Dio errors
        rethrow;
      }
    } catch (e) {
      // Optional: handle non-Dio exceptions
      rethrow;
    }
  }

  @override
  Future<BaseResponse> logout() async {
    final headers = await buildCommonHeaders();

    var dioCall =
        dioClient.post(ApiEndpoints.logout, options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<GetCookbooksResponse> fetchCookbooks(
      FetchCookbookRequest queryParam) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(ApiEndpoints.cookbooks,
        queryParameters: queryParam.toJson(),
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => GetCookbooksResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ForgotPasswordResponse> forgotPassword(String email) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.post(ApiEndpoints.forgotPassword,
        data: {
          'email': email,
        },
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => ForgotPasswordResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> addShoppingItems(
      int shoppingListId, ShoppingItemRequest body) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.post(ApiEndpoints.shoppingItems(shoppingListId),
        data: body.toJson(), options: Options(headers: headers));
    try {
      final response = await callApiWithErrorParser(dioCall);
      return response.statusCode == 200;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> addPantryItems(int pantryListId, PantryItemsRequest body) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.post(ApiEndpoints.pantryItems(pantryListId),
        data: body.toJson(), options: Options(headers: headers));
    try {
      final response = await callApiWithErrorParser(dioCall);
      return response.statusCode == 200;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<RecipeResponse> getRecipes(
      String cookbookId, PaginationQueryParam queryParam) async {
    final headers = await buildCommonHeaders();
    headers['id'] = cookbookId;
    // headers['cuisineId'] = cuisineId;
    // headers['categoryId'] = categoryId;
    // headers['search'] = search;
    // headers['pageNumber']=pageNumber.toString();
    // headers['pageSize']=pageSize.toString();
    // headers['itemSort']=itemSort;

    var dioCall = dioClient.get(ApiEndpoints.getRecipes(cookbookId),
        queryParameters: queryParam.toJson(),
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => RecipeResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse> createCookbook(String cookbookName) async {
    final headers = await buildCommonHeaders();
    var dioCall = dioClient.post(ApiEndpoints.cookBook,
        data: {
          'name': cookbookName.toString(),
        },
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall).then((response) {
        return BaseResponse.fromJson(response.data);
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse> deleteCookbook(String id) async {
    final headers = await buildCommonHeaders();
    var dioCall = dioClient.delete('${ApiEndpoints.cookBook}/$id',
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse> updateCookbook(
      String id, String name, File? file) async {
    final headers = await buildCommonHeaders();
    var formData = FormData.fromMap({
      'name': name,
      if (file != null && file.path.isNotEmpty)
        'CoverImage': await MultipartFile.fromFile(file.path,
            filename: file.uri.pathSegments.last),
    });
    var dioCall = dioClient.put(ApiEndpoints.updateCookBook(id),
        data: formData, options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ShoppingResponse> getShoppingList(
      PaginationQueryParam queryParam) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(ApiEndpoints.getshoppinglist,
        queryParameters: queryParam.toJson(),
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => ShoppingResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<CreateShoppingListResponse> createShoppingList(String name) async {
    final headers = await buildCommonHeaders();
    var dioCall = dioClient.post(ApiEndpoints.createshoppinglist,
        data: {
          'name': name.toString(),
        },
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall).then(
          (response) => CreateShoppingListResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse> deletePantryItems(
      int pantryListId, DeletePantryItemsRequest body) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.delete(ApiEndpoints.deletePantryItems(pantryListId),
        data: body.toJson(), options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse> deleteShoppingItems(
      int shoppingListId, DeleteShoppingItemsRequest body) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.put(
        ApiEndpoints.deleteShoppingItems(shoppingListId),
        data: body.toJson(),
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<RecipeDetailResponse> getRecipeDetail(
      {required int cookbookId, required int recipeId}) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(
        ApiEndpoints.getRecipesDetails(cookbookId, recipeId),
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => RecipeDetailResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse> updatePantry(String id, String name, File? file) async {
    final headers = await buildCommonHeaders();
    var formData = FormData.fromMap({
      'name': name,
      if (file != null && file.path.isNotEmpty)
        'CoverImage': await MultipartFile.fromFile(file.path,
            filename: file.uri.pathSegments.last),
    });
    var dioCall = dioClient.put(ApiEndpoints.updatePantry(id),
        data: formData, options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse> updateShopping(
      String id, String name, File? file) async {
    final headers = await buildCommonHeaders();
    var formData = FormData.fromMap({
      'name': name,
      if (file != null && file.path.isNotEmpty)
        'CoverImage': await MultipartFile.fromFile(file.path,
            filename: file.uri.pathSegments.last),
    });
    var dioCall = dioClient.put(ApiEndpoints.updateShopping(id),
        data: formData, options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<SearchItemsResponse> searchItems(String searchQuery) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(ApiEndpoints.searchItems(searchQuery),
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => SearchItemsResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<CategoryResponse> fetchCategories() async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(
      ApiEndpoints.categories,
      options: Options(headers: headers),
    );
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => CategoryResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<CuisinesResponse> fetchCuisines() async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(
      ApiEndpoints.cuisines,
      options: Options(headers: headers),
    );
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => CuisinesResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<CreateRecipeResponse> createRecipe(
      CreateRecipeRequest request, int cookbookId) async {
    final headers = await buildCommonHeaders();
    headers['id'] = cookbookId.toString();

    var dioCall = dioClient.post(
      ApiEndpoints.createRecipe(cookbookId),
      data: await request.toFormData(),
      options: Options(
        headers: headers,
        validateStatus: (status) {
          return status != null && status < 500;
        },
      ),
    );
    try {
      return callApiWithErrorParser(dioCall).then((response) {
        if (response.data != null) {
          return CreateRecipeResponse.fromJson(response.data);
        } else {
          return CreateRecipeResponse(
            success: false,
            message:
                MessageResponse(error: ['Unknown server error'], general: null),
            data: null,
            status: response.statusCode ?? 500,
          );
        }
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<RecipeDeleteResponse> deleteRecipe(
      {required int cookbookId, required int recipeId}) async {
    final headers = await buildCommonHeaders();
    headers['id'] = cookbookId.toString();

    var dioCall = dioClient.delete(
      'api/cookbook/$cookbookId/recipe/$recipeId',
      options: Options(headers: headers),
    );
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => RecipeDeleteResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<CreateRecipeResponse> updateRecipe(
      CreateRecipeRequest request, int cookbookId, int recipeId) async {
    final headers = await buildCommonHeaders();
    headers['id'] = cookbookId.toString();

    var dioCall = dioClient.put(
      ApiEndpoints.updateRecipe(cookbookId, recipeId),
      data: await request.toFormData(),
      options: Options(
        headers: headers,
        validateStatus: (status) {
          return status != null && status < 500;
        },
      ),
    );
    try {
      return callApiWithErrorParser(dioCall).then((response) {
        if (response.data != null) {
          return CreateRecipeResponse.fromJson(response.data);
        } else {
          return CreateRecipeResponse(
            success: false,
            message: MessageResponse(error: ['Unknown server error'], general: null),
            data: null,
            status: response.statusCode ?? 500,
          );
        }
      });
    } catch (e) {
      rethrow;
    }
  }

  //User profile
  @override
  Future<UserProfileResponse> userProfile() async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(
      ApiEndpoints.userProfile,
      options: Options(headers: headers),
    );
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => UserProfileResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  //post askAi api
  @override
  Future<AskAiResponse> askAi(AskAiQueryParam queryParam) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.post(ApiEndpoints.askAi,
        data: queryParam.toJson(), options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => AskAiResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  //get askAiThreads api
  @override
  Future<GetAskAiResponse> getAskAiThreads(
      PaginationQueryParam queryParam) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(ApiEndpoints.askAiThreads,
        queryParameters: queryParam.toJson(),
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => GetAskAiResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  //get askAiMessages api
  @override
  Future<GetAskAiThreadMessagesResponse> getAskAiMessages(
      int id, PaginationQueryParam queryParam) async {
    final headers = await buildCommonHeaders();
    final queryParams = queryParam.toJson();

    // Debug logging
    print('=== AskAI Messages API Call ===');
    print('Thread ID: $id');
    print('Query Parameters: $queryParams');
    print('Endpoint: ${ApiEndpoints.askAiMessages(id)}');
    print('===============================');

    var dioCall = dioClient.get(ApiEndpoints.askAiMessages(id),
        queryParameters: queryParams, options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall).then(
          (response) => GetAskAiThreadMessagesResponse.fromJson(response.data));
    } catch (e) {
      print('AskAI Messages API Error: $e');
      rethrow;
    }
  }

//Reset Password
  @override
  Future<BaseResponse> resetPassword(String currentPassword, String newPassword,
      String newConfirmPassword) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.put(ApiEndpoints.resetPassword,
        data: {
          'currentPassword': currentPassword,
          'newPassword': newPassword,
          'newConfirmPassword': newConfirmPassword,
        },
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  //Delete Account
  @override
  Future<BaseResponse> deleteAccount() async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.delete(ApiEndpoints.deleteAccount,
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  //Plans
  @override
  Future<PlansResponse> plans() async {
    final headers = await buildCommonHeaders();

    var dioCall =
        dioClient.get(ApiEndpoints.plans, options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => PlansResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  //Update user profile
  @override
  Future<BaseResponse> updateUserProfile(UpdateProfileRequest request) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.put(
      ApiEndpoints.updateUserProfile,
      data: await request.toFormData(),
      options: Options(
        headers: headers,
        validateStatus: (status) {
          return status != null && status < 500;
        },
      ),
    );
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  //User types
  @override
  Future<UserTypesResponse> userTypes() async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(ApiEndpoints.userTypes,
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => UserTypesResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse> deleteShoppingList(String id) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.delete(ApiEndpoints.deleteShoppingList(id),
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ShoppingListItemResponse> fetchShoppingListsItems(
      int shoppingListId, PaginationQueryParam queryParam) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(
        ApiEndpoints.fetchShoppingListsItems(shoppingListId),
        queryParameters: queryParam.toJson(),
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => ShoppingListItemResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse> createPantryList(String name) async {
    final headers = await buildCommonHeaders();
    var dioCall = dioClient.post(ApiEndpoints.createPantry,
        data: {
          'name': name.toString(),
        },
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<PantryResponse> getPantryList(PaginationQueryParam queryParam) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(ApiEndpoints.pantryList,
        queryParameters: queryParam.toJson(),
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => PantryResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse> deletePantryList(String id) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.delete(ApiEndpoints.deletePantryList(id),
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<PantryListItemResponse> fetchPantryListsItems(
      int pantryListId, PaginationQueryParam queryParam) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(
        ApiEndpoints.fetchPantryListsItems(pantryListId),
        queryParameters: queryParam.toJson(),
        options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => PantryListItemResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<RecipeDetailResponse> clipperRecipe({required String url}) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(ApiEndpoints.recipeClipper(url),
        options: Options(headers: headers),
      queryParameters: {'recipeUrl': url}, // pass as query param

    );
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => RecipeDetailResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }


  @override
  Future<BaseResponse> deleteThread(DeleteThreadRequest request) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.patch(ApiEndpoints.deleteThread,
        data: request.toJson(), options: Options(headers: headers));
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse> importCookBook(File file, int cookBookId) async {
    final headers = await buildCommonHeaders();

    final formMap = <String, dynamic>{};

    if (cookBookId != 0) {
      formMap['CookBookId'] = cookBookId;
    }

    if (file.path.isNotEmpty) {
      formMap['File'] = await MultipartFile.fromFile(
        file.path,
        filename: file.uri.pathSegments.last,
      );
    }

    final formData = FormData.fromMap(formMap);

    final dioCall = dioClient.post(
      ApiEndpoints.importCookBook,
      data: formData,
      options: Options(headers: headers),
    );

    try {
      final response = await callApiWithErrorParser(dioCall);
      return BaseResponse.fromJson(response.data);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<SyncListResponse> fetchSyncList() async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.get(ApiEndpoints.syncListItem,
      options: Options(headers: headers),

    );
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => SyncListResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse> syncData(Map<String, List<int>> selectedItems) async {
    final headers = await buildCommonHeaders();

    var dioCall = dioClient.post(ApiEndpoints.syncListItem,
      options: Options(headers: headers),
      data: selectedItems

    );
    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => BaseResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }
  }


  @override
  Future<HomeModel> banner(HomeQueryParam queryParam) async {
    final headers = await buildCommonHeaders();
     try {
      final response = await callApiWithErrorParser(
        dioClient.get(
          ApiEndpoints.bannerApi,
          queryParameters: queryParam.toJson(),
          options: Options(headers: headers),
        ),
      );
      return HomeModel.fromJson(response.data);
    } on DioError catch (dioError) {
      if (dioError.response?.statusCode == 403) {
        // Handle 403 error explicitly
        throw Exception(
            "Access Denied: Invalid credentials or forbidden access.");
      } else {
        // Re-throw other Dio errors
        rethrow;
      }
    } catch (e) {
      // Optional: handle non-Dio exceptions
      rethrow;
    }
  }

  Future<NutritionInfoResponse> getNutritions(  {required int recipeId}) async {
    final headers = await buildCommonHeaders();

    try {
      final response = await callApiWithErrorParser(
        dioClient.get(
          ApiEndpoints.nutritions(recipeId),
           options: Options(headers: headers),
        ),
      );
      return NutritionInfoResponse.fromJson(response.data);
    } on DioError catch (dioError) {
      if (dioError.response?.statusCode == 403) {
        // Handle 403 error explicitly
        throw Exception(
            "Access Denied: Invalid credentials or forbidden access.");
      } else {
        // Re-throw other Dio errors
        rethrow;
      }
    } catch (e) {
      // Optional: handle non-Dio exceptions
      rethrow;
    }
  }
}
